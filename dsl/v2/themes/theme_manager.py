"""
Animation Theme Manager
主题管理器，提供统一的视觉风格配置系统
"""

import json
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Optional

import yaml
from manim import *

logger = logging.getLogger(__name__)


@dataclass
class ColorPalette:
    """颜色调色板配置"""

    primary: str = DARK_BLUE
    secondary: str = GREEN
    accent: str = YELLOW
    background: str = BLACK
    surface: str = DARK_GRAY
    text_primary: str = WHITE
    text_secondary: str = GRAY_C
    blockquote: str = GRAY_C
    error: str = RED
    warning: str = ORANGE
    success: str = GREEN
    # 图表颜色序列
    cyclic_colors: list[str] = field(default_factory=lambda: [BLUE, RED, GREEN, YELLOW_A, PURPLE, ORANGE, TEAL])

    # 时间轴颜色配置
    timeline_background: str = "#F7F6F1"  # 时间轴背景颜色
    timeline_connector_color: str = "#4B90E2"  # 连接线颜色，可以复用但特殊指定


@dataclass
class Typography:
    """字体配置"""

    primary_font: str = "Microsoft YaHei"
    secondary_font: str = "LXGW WenKai Mono"
    code_font: str = "LXGW WenKai Mono"
    table_font: str = "Microsoft YaHei"

    # 发光字体配置 - 白底发光Arial字体
    glow_font: str = "Arial"
    glow_color: str = "#E0E0E0"  # 更深的浅灰色，与白色有更好的对比度
    glow_stroke_color: str = "#236B8E"  # 钢青色发光边框（比天蓝色更深）
    glow_stroke_width: float = 2.0  # 增大发光区域
    glow_stroke_opacity: float = 1
    glow_weight: str = BOLD

    # 字号配置
    display_size: int = 56
    h1_size: int = 48
    h2_size: int = 40
    h3_size: int = 32
    h4_size: int = 28
    body_size: int = 24
    small_size: int = 20
    code_size: int = 20

    # 行距配置
    line_spacing: float = 1.2
    paragraph_spacing: float = 0.8

    # 时间轴字体配置 - 仅保留特殊字体
    timeline_year_font: str = "Georgia"  # 年份标签使用特殊字体

    # 时间轴字体大小 - 只保留与基础字体大小不同的配置
    timeline_main_title_size: int = 52  # 主标题大小


@dataclass
class Spacing:
    """间距配置"""

    xs: float = 0.1
    sm: float = 0.2
    md: float = 0.4
    lg: float = 0.8
    xl: float = 1.2
    xxl: float = 1.6

    # 特定组件间距
    list_item_spacing: float = 0.2
    chart_element_spacing: float = 0.5
    section_spacing: float = 1.0

    # 时间轴间距配置
    timeline_year: float = 0.7
    timeline_title: float = 0.3
    timeline_description: float = 0.25
    timeline_title_subtitle: float = 0.3
    timeline_title_edge: float = 0.5
    timeline_final: float = 1.0


@dataclass
class AnimationSettings:
    """动画配置"""

    default_duration: float = 2.0
    fade_duration: float = 1.0
    slide_duration: float = 1.5
    highlight_duration: float = 0.5
    lag_ratio: float = 1.0

    # 特定元素类型的动画时长
    list_item_fade_duration: float = 0.3
    code_block_duration: float = 1.5
    text_write_speed: float = 0.03  # 每个字符的时间
    text_write_min_duration: float = 0.3  # 最小写入时间

    # 时间轴动画时长
    timeline_title: float = 2.0
    timeline_title_wait: float = 0.5
    timeline_node: float = 0.5
    timeline_info: float = 0.7
    timeline_element_wait: float = 0.3
    timeline_final_wait: float = 1.0
    timeline_final_adjustment: float = 1.5
    timeline_end_wait: float = 0.5

    # 缓动函数
    # 可选值: "smooth", "ease_in", "ease_out", "ease_in_out", "ease_in_out_sine", "linear"
    default_rate_func: str = "smooth"


@dataclass
class ComponentStyles:
    """组件特定样式"""

    # 背景不透明度，用于控制渐变背景效果
    background_opacity: float = 0.8

    # 比较面板（side_by_side_comparison）设置
    comparison: dict[str, Any] = field(
        default_factory=lambda: {
            # 面板背景样式
            "panel_background_opacity": 0.8,  # 默认面板背景不透明度
            "panel_corner_radius": 0.5,  # 面板圆角半径
            # 内容背景样式（例如列表项或代码片段的背景）
            "content_background_opacity": 0.7,  # 内容背景不透明度
        }
    )
    background_corner_radius: float = 0.2
    stroke_width: float = 2.0

    # 列表样式
    list_background_opacity: float = 0.7
    list_corner_radius: float = 0.2
    list_padding: float = 0.2
    list_indent: float = 0.2  # 列表项缩进

    # 表格样式
    table_line_width: float = 1.5
    table_cell_padding: float = 0.4
    table_row_padding: float = 0.2
    table_header_background: str = GRAY_D  # 表头背景色

    # 代码块样式
    code_block_background: str = GRAY_E
    code_block_corner_radius: float = 0.2
    code_block_padding: float = 0.3

    # 引用块样式
    blockquote_background: str = GRAY_E
    blockquote_border_width: float = 3.0
    blockquote_border_color: str = BLUE

    # 图表样式
    chart_bar_width: float = 0.3
    chart_line_width: float = 4.0
    axis_stroke_width: float = 3.0

    # 时间轴样式
    timeline: dict[str, Any] = field(
        default_factory=lambda: {
            # 节点和线条
            "node_radius": 0.6,
            "node_spacing": 6.0,
            "node_stroke_width": 5,
            "connector_stroke_width": 4,
            # 年份标签样式
            "year_bg_width": 1.6,
            "year_bg_height": 0.6,
            "year_corner_radius": 0.5,
            "year_text_scale_factor": 0.7,
            "year_scale_factor": 0.8,
            # 描述文本样式
            "desc_scale_factor": 0.45,
            "desc_shift_amount": 0.2,
            # 动画参数
            "title_shift_amount": 0.2,
            "title_lag_ratio": 0.3,
            "emoji_scale_factor": 1.0,
            "node_lag_ratio": 0.2,
            "info_lag_ratio": 0.2,
            # 布局参数
            "screen_width_factor": 0.95,
        }
    )


@dataclass
class Theme:
    """完整主题配置"""

    name: str
    colors: ColorPalette
    typography: Typography
    spacing: Spacing
    animation: AnimationSettings
    components: ComponentStyles

    def to_dict(self) -> dict[str, Any]:
        """将主题转换为字典格式"""
        return {
            "name": self.name,
            "colors": self.colors.__dict__,
            "typography": self.typography.__dict__,
            "spacing": self.spacing.__dict__,
            "animation": self.animation.__dict__,
            "components": self.components.__dict__,
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "Theme":
        """从字典创建主题"""
        return cls(
            name=data["name"],
            colors=ColorPalette(**data["colors"]),
            typography=Typography(**data["typography"]),
            spacing=Spacing(**data["spacing"]),
            animation=AnimationSettings(**data["animation"]),
            components=ComponentStyles(**data["components"]),
        )

    def clone(self, name: str = None) -> "Theme":
        """克隆当前主题，可选择指定新名称"""
        new_name = name or f"{self.name}_copy"
        return Theme(
            name=new_name,
            colors=ColorPalette(**self.colors.__dict__),
            typography=Typography(**self.typography.__dict__),
            spacing=Spacing(**self.spacing.__dict__),
            animation=AnimationSettings(**self.animation.__dict__),
            components=ComponentStyles(**self.components.__dict__),
        )


class ThemeManager:
    """主题管理器"""

    _instance = None
    _themes: dict[str, Theme] = {}
    _current_theme: Optional[Theme] = None

    def __new__(cls):
        # 单例模式实现
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化主题管理器"""
        self._themes = {}
        self._load_builtin_themes()

    def _load_builtin_themes(self):
        """加载内置主题"""
        # 默认主题
        default_theme = Theme(
            name="default",
            colors=ColorPalette(),
            typography=Typography(),
            spacing=Spacing(),
            animation=AnimationSettings(),
            components=ComponentStyles(),
        )

        # 深色主题
        dark_theme = Theme(
            name="dark",
            colors=ColorPalette(
                primary=BLUE_D,
                secondary=GREEN_D,
                accent=YELLOW_D,
                background=BLACK,
                surface=DARK_GRAY,
                text_primary=WHITE,
                text_secondary=GRAY_C,
                cyclic_colors=[BLUE_D, RED_D, GREEN_D, YELLOW_D, PURPLE_D, ORANGE, TEAL_D],
                # 时间轴深色主题颜色
                timeline_background="#151515",
                timeline_connector_color="#3A7BD5",
            ),
            typography=Typography(),
            spacing=Spacing(),
            animation=AnimationSettings(
                default_duration=2.5,
                # 深色主题下动画时长稍长一些
                timeline_title=2.2,
                timeline_node=0.6,
                timeline_info=0.8,
            ),
            components=ComponentStyles(
                timeline={
                    # 深色主题特殊组件样式
                    "node_radius": 0.3,  # 更大的节点
                    "node_spacing": 5.5,  # 更紧凑的布局
                    "node_stroke_width": 4,  # 更细的轮廓线
                    "connector_stroke_width": 5,  # 更宽的连接线
                    "year_corner_radius": 0.3,  # 更圆的年份标签
                    "emoji_scale_factor": 0.7,  # 更大的表情符号
                    "year_bg_width": 1.8,  # 更宽的年份背景
                    "desc_scale_factor": 0.5,  # 更大的描述文本
                },
                # 深色主题下比较面板的不透明度设置
                comparison={
                    "panel_background_opacity": 0.8,  # 深色主题下面板背景不透明度
                    "panel_corner_radius": 0.5,
                    "content_background_opacity": 0.7,  # 内容背景不透明度
                },
            ),
        )

        # 亮色主题
        light_theme = Theme(
            name="light",
            colors=ColorPalette(
                primary=BLUE_E,
                secondary=GREEN_C,
                accent=ORANGE,
                background=WHITE,
                surface=LIGHT_GRAY,
                text_primary=BLACK,
                text_secondary=DARK_GRAY,
                cyclic_colors=[BLUE_C, RED_C, GREEN_C, YELLOW_B, PURPLE_C, ORANGE, TEAL_C],
                # 时间轴亮色主题颜色
                timeline_background="#F7F6F1",
                timeline_connector_color="#4B90E2",
            ),
            typography=Typography(),
            spacing=Spacing(),
            animation=AnimationSettings(),
            components=ComponentStyles(
                background_opacity=0.9,
                timeline={
                    # Light theme 特殊组件样式
                    "node_radius": 0.3,
                    "node_spacing": 6.0,
                    "node_stroke_width": 2,  # 更细的轮廓线
                    "connector_stroke_width": 3,  # 更细的连接线
                    "year_corner_radius": 0.2,  # 稍微不同的圆角
                },
                # 亮色主题下比较面板需要更高不透明度以提高可读性
                comparison={
                    "panel_background_opacity": 0.8,  # 更高的不透明度
                    "panel_corner_radius": 0.5,
                    "content_background_opacity": 0.9,  # 更高的内容背景不透明度
                },
            ),
        )

        # 简约主题
        minimal_theme = Theme(
            name="minimal",
            colors=ColorPalette(
                primary=GRAY_D,
                secondary=GRAY_C,
                accent=BLACK,
                background=WHITE,
                surface=LIGHT_GRAY,
                text_primary=BLACK,
                text_secondary=GRAY_D,
                cyclic_colors=[GRAY_D, GRAY_C, GRAY_B, GRAY_A, DARK_GRAY, LIGHT_GRAY, WHITE],
            ),
            typography=Typography(primary_font="Helvetica", line_spacing=1.5, body_size=22),
            spacing=Spacing(),
            animation=AnimationSettings(default_duration=1.5),
            components=ComponentStyles(background_opacity=0.0, stroke_width=1.0),
        )

        # 演示主题（更加生动）
        presentation_theme = Theme(
            name="presentation",
            colors=ColorPalette(
                primary=BLUE_A,
                secondary=GREEN_A,
                accent=YELLOW_A,
                background=DARK_BLUE,
                surface=BLUE_E,
                text_primary=WHITE,
                text_secondary=LIGHT_GRAY,
                cyclic_colors=[BLUE_A, RED_A, GREEN_A, YELLOW_A, PURPLE_A, ORANGE, TEAL_A],
            ),
            typography=Typography(h1_size=52, h2_size=44, body_size=28, line_spacing=1.3),
            spacing=Spacing(md=0.6, lg=1.0, xl=1.6),
            animation=AnimationSettings(default_duration=2.5, fade_duration=1.2),
            components=ComponentStyles(background_opacity=0.8, background_corner_radius=0.3),
        )

        # 注册所有内置主题
        for theme in [default_theme, dark_theme, light_theme, minimal_theme, presentation_theme]:
            self._themes[theme.name] = theme

        # 设置默认主题
        self._current_theme = default_theme

    def register_theme(self, theme: Theme) -> bool:
        """注册新主题"""
        if theme.name in self._themes:
            logger.warning(f"主题 '{theme.name}' 已存在，将被覆盖")
        self._themes[theme.name] = theme
        return True

    def get_theme(self, name: str) -> Optional[Theme]:
        """获取指定主题"""
        return self._themes.get(name)

    def set_theme(self, name: str) -> bool:
        """设置当前主题"""
        if name in self._themes:
            self._current_theme = self._themes[name]
            logger.info(f"已切换到主题: {name}")
            return True
        logger.warning(f"主题 '{name}' 不存在，保持当前主题不变")
        return False

    def get_current_theme(self) -> Theme:
        """获取当前主题"""
        if self._current_theme is None:
            self._current_theme = self._themes["default"]
        return self._current_theme

    def list_themes(self) -> list[str]:
        """列出所有可用主题"""
        return list(self._themes.keys())

    def save_theme(self, theme: Theme, filepath: str) -> bool:
        """保存主题到文件"""
        try:
            path = Path(filepath)
            with open(path, "w", encoding="utf-8") as f:
                json.dump(theme.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"主题 '{theme.name}' 已保存到 {filepath}")
            return True
        except Exception as e:
            logger.error(f"保存主题到 {filepath} 失败: {str(e)}")
            return False

    def load_theme_from_file(self, filepath: str) -> Optional[Theme]:
        """从文件加载主题"""
        try:
            path = Path(filepath)
            with open(path, encoding="utf-8") as f:
                data = json.load(f)
            theme = Theme.from_dict(data)
            self.register_theme(theme)
            logger.info(f"已从 {filepath} 加载主题 '{theme.name}'")
            return theme
        except Exception as e:
            logger.error(f"从 {filepath} 加载主题失败: {str(e)}")
            return None

    def override_theme_value(self, path: str, value: Any) -> bool:
        """覆盖当前主题中的特定值

        Args:
            path: 属性路径，例如 'colors.primary' 或 'typography.body_size'
            value: 要设置的新值

        Returns:
            bool: 是否成功覆盖
        """
        if self._current_theme is None:
            return False

        parts = path.split(".")
        if len(parts) != 2:
            logger.error(f"无效的属性路径: {path}. 应为'category.attribute'格式")
            return False

        category, attribute = parts

        # 获取类别对象
        if not hasattr(self._current_theme, category):
            logger.error(f"主题中不存在类别: {category}")
            return False

        category_obj = getattr(self._current_theme, category)

        # 设置属性
        if not hasattr(category_obj, attribute):
            logger.error(f"类别 {category} 中不存在属性: {attribute}")
            return False

        setattr(category_obj, attribute, value)
        logger.info(f"已覆盖主题属性 {path} = {value}")
        return True


# 全局主题管理器实例
theme_manager = ThemeManager()


def get_current_theme() -> Theme:
    """获取当前主题的便捷函数"""
    return theme_manager.get_current_theme()


def set_theme(name: str) -> bool:
    """设置主题的便捷函数"""
    return theme_manager.set_theme(name)


def get_theme_value(path: str, default_value: Any = None) -> Any:
    """获取主题中的特定值
    例如: get_theme_value("colors.primary") 返回当前主题的主色
    支持字典嵌套访问，如: get_theme_value("components.comparison.panel_background_opacity")
    """
    theme = get_current_theme()
    parts = path.split(".")

    current = theme
    for part in parts:
        if isinstance(current, dict) and part in current:
            current = current[part]
        elif hasattr(current, part):
            current = getattr(current, part)
        else:
            return default_value

    return current


def override_theme_value(path: str, value: Any) -> bool:
    """覆盖当前主题中的特定值的便捷函数"""
    return theme_manager.override_theme_value(path, value)


config = yaml.safe_load(open("config/config.yaml"))
set_theme(config["theme"]["name"])
