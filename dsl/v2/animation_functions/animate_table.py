"""
effect: |
    在Manim场景中创建并展示表格，支持逐行高亮显示效果。表格包含表头和数据行，单元格之间有间隙，支持黄色高亮状态。

use_cases:
    - 展示统计数据表格
    - 显示对比数据分析
    - 逐步突出重要数据行
    - 展示结构化信息

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  headers:
    type: List[str]
    desc: 表格表头列表
    required: true
  data:
    type: List[List[str]]
    desc: 表格数据，每个子列表代表一行数据
    required: true
  title:
    type: str
    desc: 表格标题
    default: None
  highlight_rows:
    type: List[int]
    desc: 需要高亮的行索引列表（从0开始，不包括表头）
    default: []
  cell_spacing:
    type: float
    desc: 单元格之间的间距
    default: 0.2
  highlight_color:
    type: str
    desc: 高亮颜色，支持Manim颜色常量
    default: YELLOW
  narration:
    type: str
    desc: 在表格展示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_table
    params:
      headers: ["城市", "4月入职", "5月离职", "5月入职", "5月离职", "6月入职", "6月离职", "主要离职原因"]
      data:
        - ["北京", "23", "11", "15", "7", "17", "3", "家庭原因"]
        - ["上海", "43", "2", "12", "8", "19", "11", "薪资原因"]
        - ["深圳", "11", "3", "15", "2", "16", "5", "个人发展"]
        - ["杭州", "15", "1", "19", "3", "15", "0", "家庭原因"]
        - ["武汉", "17", "0", "9", "7", "7", "8", "晋升"]
        - ["广州", "9", "1", "3", "0", "2", "2", "个人问题"]
        - ["合计", "118", "18", "73", "27", "76", "29", "/"]
      title: 城市职业变化统计表
      highlight_rows: [1]
      narration: 让我们来看看这个城市职业变化的统计表格。
  - type: animate_table
    params:
      headers: ["产品", "Q1销量", "Q2销量", "增长率"]
      data:
        - ["产品A", "100", "120", "20%"]
        - ["产品B", "80", "95", "18.8%"]
        - ["产品C", "60", "90", "50%"]
      highlight_rows: [0, 2]
      narration: 这是产品销量的对比分析表格。

notes:

  - 表格内容不要超过8个字符
  - 表格会自动调整尺寸以适应屏幕
  - 高亮行会以黄色背景显示
  - 单元格之间的间距可以调整
  - 支持中文内容显示
  - 表格展示后会逐行进行高亮动画
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from loguru import logger
from manim import *
from dsl.v2.animation_functions.animate_markdown import create_glow_text_simple


def _create_rounded_cell(
    content: str, width: float, height: float, cell_type: str = "data", cell_spacing: float = 0.2
) -> VGroup:
    """
    创建单个圆角表格单元格（参考图片样式）

    Args:
        content: 单元格内容
        width: 单元格宽度
        height: 单元格高度
        cell_type: 单元格类型 ("header", "data", "highlight", "total")
        cell_spacing: 单元格间距

    Returns:
        VGroup: 包含背景和文本的单元格组
    """
    # 减去间距的实际单元格尺寸
    actual_width = width - cell_spacing
    actual_height = height - cell_spacing

    # 根据类型设置颜色
    if cell_type == "header":
        # 蓝色表头
        fill_color = "#4a90e2"
        text_color = WHITE
        fill_opacity = 1.0
        font_weight = "bold"
    elif cell_type == "highlight":
        # 黄色高亮行
        fill_color = "#ffd700"
        text_color = "#333333"
        fill_opacity = 1.0
        font_weight = "normal"
    elif cell_type == "total":
        # 合计行（浅蓝色）
        fill_color = "#e8f4fd"
        text_color = "#2c5aa0"
        fill_opacity = 1.0
        font_weight = "bold"
    else:
        # 普通数据行（白色）
        fill_color = WHITE
        text_color = "#333333"
        fill_opacity = 1.0
        font_weight = "normal"

    # 创建圆角矩形背景
    cell_bg = RoundedRectangle(
        width=actual_width,
        height=actual_height,
        corner_radius=0.1,
        fill_color=fill_color,
        fill_opacity=fill_opacity,
        stroke_width=0,
    )

    # 添加阴影效果（通过偏移的深色矩形模拟）
    shadow = RoundedRectangle(
        width=actual_width,
        height=actual_height,
        corner_radius=0.1,
        fill_color="#00000020",
        fill_opacity=0.15,
        stroke_width=0,
    )
    shadow.shift(DOWN * 0.02 + RIGHT * 0.02)

    # 创建文本 - 增大字体
    font_size = 25 if cell_type == "header" else 22

    cell_text = create_glow_text_simple(
        content, 
        font_size=font_size
    )

    # 如果文本太长，缩放以适应单元格
    if cell_text.width > actual_width * 0.85:
        cell_text.scale(actual_width * 0.85 / cell_text.width)
    if cell_text.height > actual_height * 0.7:
        cell_text.scale(actual_height * 0.7 / cell_text.height)

    # 创建单元格组（阴影在底层）
    cell_group = VGroup(shadow, cell_bg, cell_text)
    cell_text.move_to(cell_bg.get_center())

    return cell_group


def _create_table_with_spacing(
    headers: list[str],
    data: list[list[str]],
    title: Optional[str] = None,
    highlight_rows: list[int] = None,
    cell_spacing: float = 0.2,
) -> tuple[VGroup, list[VGroup], list[VGroup]]:
    """
    创建带间距的表格结构（完全参考图片样式）

    Args:
        headers: 表头列表
        data: 数据行列表
        title: 表格标题
        highlight_rows: 高亮行索引列表
        cell_spacing: 单元格间距

    Returns:
        tuple: (完整表格组, 数据行组列表, 高亮行组列表)
    """
    if highlight_rows is None:
        highlight_rows = []

    # 设置单元格尺寸 - 第一列和后续列使用不同宽度
    base_cell_width = 1.8  # 第一列的基础宽度
    wide_cell_width = base_cell_width * 1.5  # 第二列往后的宽度（1.5倍）
    cell_height = 0.8

    # 存储所有行
    all_rows = []
    data_row_groups = []
    highlight_row_groups = []

    # 创建表头行
    header_cells = []
    for col_idx, header in enumerate(headers):
        # 第一列使用基础宽度，其他列使用1.5倍宽度
        width = base_cell_width if col_idx == 0 else wide_cell_width
        cell = _create_rounded_cell(
            content=header, width=width, height=cell_height, cell_type="header", cell_spacing=cell_spacing
        )
        header_cells.append(cell)

    header_row = VGroup(*header_cells)
    header_row.arrange(RIGHT, buff=cell_spacing)
    all_rows.append(header_row)

    # 创建数据行
    for row_idx, row_data in enumerate(data):
        row_cells = []

        # 判断行类型
        is_highlighted = row_idx in highlight_rows
        is_total_row = len(row_data) > 0 and str(row_data[0]).lower() in ["合计", "总计", "total", "sum"]

        if is_total_row:
            cell_type = "total"
        elif is_highlighted:
            cell_type = "highlight"
        else:
            cell_type = "data"

        for col_idx, cell_data in enumerate(row_data):
            # 第一列使用基础宽度，其他列使用1.5倍宽度
            width = base_cell_width if col_idx == 0 else wide_cell_width
            cell = _create_rounded_cell(
                content=str(cell_data),
                width=width,
                height=cell_height,
                cell_type=cell_type,
                cell_spacing=cell_spacing,
            )
            row_cells.append(cell)

        data_row = VGroup(*row_cells)
        data_row.arrange(RIGHT, buff=cell_spacing)
        all_rows.append(data_row)
        data_row_groups.append(data_row)

        # 如果是高亮行，记录下来
        if is_highlighted:
            highlight_row_groups.append(data_row)

    # 排列所有行
    table_group = VGroup(*all_rows)
    table_group.arrange(DOWN, buff=cell_spacing)

    # 添加标题
    final_group = VGroup()
    if title:
        title_text = create_glow_text_simple(title, font_size=36)
        final_group.add(title_text)
        final_group.add(table_group)
        final_group.arrange(DOWN, buff=0.7)
    else:
        final_group = table_group

    # 缩放以适应屏幕 - 放宽限制以允许更大的表格
    if final_group.width > 14.5:  # 从13增加到14.5
        final_group.scale(14.5 / final_group.width)
    if final_group.height > 9:  # 从8增加到9
        final_group.scale(9 / final_group.height)

    # 居中显示
    final_group.move_to(ORIGIN)

    return final_group, data_row_groups, highlight_row_groups


def _modify_row_colors(row: VGroup, cell_type: str) -> VGroup:
    """
    修改行中所有单元格的颜色以匹配指定的单元格类型
    
    Args:
        row: 要修改的行VGroup
        cell_type: 单元格类型 ("header", "data", "highlight", "total")
    
    Returns:
        VGroup: 修改后的行
    """
    # 根据类型设置颜色
    if cell_type == "header":
        fill_color = "#4a90e2"
        text_color = WHITE
    elif cell_type == "highlight":
        fill_color = "#ffd700"  
        text_color = "#333333"
    elif cell_type == "total":
        fill_color = "#e8f4fd"
        text_color = "#2c5aa0"
    else:
        fill_color = WHITE
        text_color = "#333333"
    
    # 修改每个单元格的颜色
    for cell_group in row:
        if hasattr(cell_group, 'submobjects') and len(cell_group.submobjects) >= 2:
            # cell_group 结构: [shadow, cell_bg, cell_text]
            if len(cell_group.submobjects) >= 3:
                cell_bg = cell_group.submobjects[1]  # 背景矩形
                cell_text = cell_group.submobjects[2]  # 文本
                
                # 修改背景颜色
                cell_bg.set_fill(fill_color, opacity=1.0)
                # 修改文本颜色 - 但保持glow效果
                if hasattr(cell_text, 'submobjects') and len(cell_text.submobjects) >= 2:
                    # 如果是glow text (VGroup with glow_text and main_text)
                    main_text = cell_text.submobjects[1] if len(cell_text.submobjects) > 1 else cell_text.submobjects[0]
                    main_text.set_color(WHITE)  # 保持白色主文本以维持glow效果
                else:
                    # 如果是普通text
                    cell_text.set_color(WHITE)  # 保持白色以维持glow效果
    
    return row


def animate_table(
    scene: "FeynmanScene",
    headers: list[str],
    data: list[list[str]],
    title: Optional[str] = None,
    highlight_rows: list[int] = None,
    cell_spacing: float = 0.2,
    highlight_color: str = "YELLOW",
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    在Manim场景中创建并展示表格，支持逐行高亮显示效果（完全参考图片样式）

    Args:
        scene: Manim场景实例
        headers: 表格表头列表
        data: 表格数据，每个子列表代表一行数据
        title: 表格标题
        highlight_rows: 需要高亮的行索引列表（从0开始，不包括表头）
        cell_spacing: 单元格之间的间距
        highlight_color: 高亮颜色
        narration: 语音旁白文本
        id: 创建的Manim Mobject的唯一标识符
    """
    try:
        logger.info(f"开始创建表格动画，标题: {title}")

        # 清理当前对象
        scene.clear_current_mobj()

        # 参数验证
        if not headers or not data:
            raise ValueError("表头和数据不能为空")

        # 验证数据行列数一致性
        expected_cols = len(headers)
        for i, row in enumerate(data):
            if len(row) != expected_cols:
                logger.warning(f"数据行 {i} 的列数 ({len(row)}) 与表头列数 ({expected_cols}) 不匹配")

        if highlight_rows is None:
            highlight_rows = []

        # 创建不高亮的表格
        normal_table, data_rows, _ = _create_table_with_spacing(
            headers=headers,
            data=data,
            title=title,
            highlight_rows=[],  # 先不高亮任何行
            cell_spacing=cell_spacing,
        )

        # 使用语音旁白和动画
        with scene.voiceover(narration) as tracker:
            # 显示表格
            scene.play(FadeIn(normal_table), run_time=1.5)
            scene.wait(0.5)

            # 逐行高亮动画
            if len(data) > 0:
                # 第一行：只有高亮动画
                row_data = data[0]
                is_total_row = len(row_data) > 0 and str(row_data[0]).lower() in ["合计", "总计", "total", "sum"]
                
                highlight_row = data_rows[0].copy()
                cell_type = "total" if is_total_row else "highlight"
                _modify_row_colors(highlight_row, cell_type)
                highlight_row.move_to(data_rows[0].get_center())
                
                scene.play(Transform(data_rows[0], highlight_row), run_time=0.4)
                scene.wait(0.1)
                
                # 从第二行开始：同时恢复上一行和高亮当前行
                for row_idx in range(1, len(data)):
                    animations = []
                    
                    # 恢复上一行到普通状态
                    prev_row_data = data[row_idx - 1]
                    prev_is_total_row = len(prev_row_data) > 0 and str(prev_row_data[0]).lower() in ["合计", "总计", "total", "sum"]
                    prev_normal_row = data_rows[row_idx - 1].copy()
                    prev_normal_cell_type = "total" if prev_is_total_row else "data"
                    _modify_row_colors(prev_normal_row, prev_normal_cell_type)
                    prev_normal_row.move_to(data_rows[row_idx - 1].get_center())
                    animations.append(Transform(data_rows[row_idx - 1], prev_normal_row))
                    
                    # 高亮当前行
                    curr_row_data = data[row_idx]
                    curr_is_total_row = len(curr_row_data) > 0 and str(curr_row_data[0]).lower() in ["合计", "总计", "total", "sum"]
                    curr_highlight_row = data_rows[row_idx].copy()
                    curr_cell_type = "total" if curr_is_total_row else "highlight"
                    _modify_row_colors(curr_highlight_row, curr_cell_type)
                    curr_highlight_row.move_to(data_rows[row_idx].get_center())
                    animations.append(Transform(data_rows[row_idx], curr_highlight_row))
                    
                    # 同时执行两个动画
                    scene.play(*animations, run_time=0.4)
                    scene.wait(0.1)
                
                # 最后恢复最后一行
                if len(data) > 0:
                    last_row_data = data[-1]
                    is_total_row = len(last_row_data) > 0 and str(last_row_data[0]).lower() in ["合计", "总计", "total", "sum"]
                    normal_row = data_rows[-1].copy()
                    normal_cell_type = "total" if is_total_row else "data"
                    _modify_row_colors(normal_row, normal_cell_type)
                    normal_row.move_to(data_rows[-1].get_center())
                    
                    scene.play(Transform(data_rows[-1], normal_row), run_time=0.3)
                    scene.wait(0.1)

        # 保存当前对象
        scene.current_mobj = normal_table
        scene.save_scene_state("table", "table")

        # 如果提供了ID，保存到对象字典
        if id:
            scene.mobj_dict[id] = normal_table
            logger.info(f"表格已保存到对象字典，ID: {id}")

        logger.info("表格动画创建完成")

    except Exception as e:
        logger.error(f"创建表格动画时发生错误: {str(e)}")

        # 错误恢复 - 显示错误信息
        error_text = create_glow_text_simple(f"表格创建失败\n{str(e)[:50]}...", font_size=24)
        error_text.move_to(ORIGIN)

        with scene.voiceover(narration or "抱歉，表格创建时出现了问题。") as tracker:  # noqa: F841
            scene.play(FadeIn(error_text))

        scene.current_mobj = error_text

        if id:
            scene.mobj_dict[id] = error_text
