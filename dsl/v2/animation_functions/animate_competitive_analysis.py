"""
effect: |
    创建多阶段的竞品对比分析动画，包括1v1对比、维度雷达图分析和综合评估结论。

use_cases:
    - 产品竞品分析报告展示
    - 技术方案选型对比分析
    - 多维度评估结果可视化
    - 决策建议和结论展示

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  analysis_data:
    type: dict
    desc: 竞品分析数据，包含当前方案、替代方案、维度对比分析和综合评估
    required: true
  narration:
    type: str
    desc: 整体分析过程的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_competitive_analysis
    params:
      analysis_data:
        当前方案:
          名称: "Quarkdown"
          定位: "通用文档格式转换器，支持多种输入输出格式"
          优势: "编程逻辑支持，广泛的格式支持，命令行操作方便集成"
          劣势: "复杂排版限制"
          适用场景: "文档转换，报告生成"
        替代方案:
          - 名称: "Pandoc"
            定位: "通用文档格式转换器，支持多种输入输出格式"
            优势: "社区成熟度，广泛的格式支持，高度灵活"
            劣势: "静态内容局限"
            适用场景: "文档转换，报告生成"
          - 名称: "Jupyter Notebook"
            定位: "代码、文本和输出结果混合编程文档"
            优势: "实时内核交互，强数据分析能力，结果可交互展示"
            劣势: "排版能力受限，难以生成专业印刷书籍"
            适用场景: "数据科学报告，研究分析"
          - 名称: "LaTeX"
            定位: "科学出版和技术文档领域的标准排版系统"
            优势: "印刷级精度，排版质量极高，公式图表处理强大"
            劣势: "学习曲线陡峭，语法复杂"
            适用场景: "学术论文，书籍出版"
        维度对比分析:
          功能特性: "Quarkdown强调编程与动态内容，Pandoc重在格式转换"
          编程能力: "Quarkdown图灵完备，Jupyter侧重数据处理"
          输出格式: "Quarkdown/Pandoc支持多种格式"
          易用性: "Quarkdown基于Markdown中等难度"
        综合评估:
          推荐指数: "4星"
          关键结论: "Quarkdown是创新型可编程排版工具"
          决策建议: "对于需工程化内容创作用户值得投入学习"
      narration: 接下来我们将进行全面的竞品对比分析，从1v1对比开始，再展示维度雷达图，最后给出综合评估结论。

notes:
  - 动画分为三个主要阶段：1v1对比、雷达图分析、综合评估
  - 会根据替代方案数量自动调整对比展示
  - 雷达图将展示五个维度的对比分析
  - 最终会给出推荐指数和决策建议
  - 调用此函数会清除屏幕上的其他内容
"""

import json
from typing import TYPE_CHECKING, Any, Dict, List, Optional

import numpy as np
from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from dsl.v2.themes.theme_utils import ThemeUtils


def _create_artistic_title(text: str, font_size: int = 24) -> VGroup:
    """创建带艺术效果的标题"""
    title_group = VGroup()
    
    # 主标题文字
    title_text = Text(
        text,
        font_size=font_size,
        color=WHITE,
        weight=BOLD
    )
    
    # 背景光晕效果
    title_bg = RoundedRectangle(
        width=title_text.width + 0.4,
        height=title_text.height + 0.2,
        corner_radius=0.1,
        fill_color=YELLOW,
        fill_opacity=0.3,
        stroke_color=GOLD,
        stroke_width=1,
        stroke_opacity=0.8
    )
    title_bg.move_to(title_text.get_center())
    
    # 装饰性小点
    dot1 = Dot(color=GOLD, radius=0.05)
    dot2 = Dot(color=GOLD, radius=0.05)
    dot1.next_to(title_bg, LEFT, buff=0.15)
    dot2.next_to(title_bg, RIGHT, buff=0.15)
    
    title_group.add(title_bg, title_text, dot1, dot2)
    return title_group


def _create_section_divider(width: float = 5.0) -> Line:
    """创建模块分割线"""
    return Line(
        start=LEFT * width/2,
        end=RIGHT * width/2,
        color=WHITE,
        stroke_width=1,
        stroke_opacity=0.4
    )


def _create_comparison_panel(
    title: str,
    content_data: Dict[str, str],
    position: np.ndarray,
    width: float,
    height: float,
    gradient_colors: List[str],
    is_current: bool = False
) -> VGroup:
    """创建单个对比面板，参考HTML设计"""
    panel_group = VGroup()
    
    # 创建渐变背景（无边框）
    panel_bg = Rectangle(
        width=width,
        height=height,
        fill_opacity=1.0,
        stroke_width=0
    )
    
    # 设置渐变色
    if len(gradient_colors) >= 2:
        panel_bg.set_fill(color=[gradient_colors[0], gradient_colors[1]], opacity=1.0)
    else:
        panel_bg.set_fill(color=gradient_colors[0], opacity=1.0)
    
    panel_bg.move_to(position)
    
    # 创建标题（只显示方案名）
    # 从title中提取方案名（去掉"当前方案："或"替代方案X："前缀）
    if "：" in title:
        solution_name = title.split("：", 1)[1]
    else:
        solution_name = title
    
    title_text = Text(
        solution_name,
        font_size=34,  # 适中大小
        color=WHITE,
        weight=BOLD
    )
    title_text.next_to(panel_bg.get_top(), DOWN, buff=0.4)
    
    # 创建内容区域，均匀分布
    content_group = VGroup()
    y_offset = 0.8  # 适中的初始偏移
    
    # 产品定位 - 使用艺术字标题
    positioning_title = _create_artistic_title("产品定位", font_size=22)
    positioning_title.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(positioning_title)
    y_offset += 0.6
    
    positioning_text = Text(
        content_data.get("定位", ""),
        font_size=17,  # 适中大小
        color=WHITE,
        line_spacing=1.15
    )
    positioning_text.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(positioning_text)
    y_offset += 0.3
    
    # 分割线
    divider1 = _create_section_divider(width * 0.75)
    divider1.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(divider1)
    y_offset += 0.6
    
    # 核心优势 - 使用艺术字标题
    advantages_title = _create_artistic_title("核心优势", font_size=22)
    advantages_title.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(advantages_title)
    y_offset += 0.45
    
    # 解析优势列表
    advantages = content_data.get('优势', '').split('，')
    for advantage in advantages:
        if advantage.strip():
            advantage_item = VGroup()
            checkmark = Text("✓", font_size=17, color=GREEN, weight=BOLD)
            advantage_text = Text(
                advantage.strip(),
                font_size=16,
                color=WHITE
            )
            advantage_item.add(checkmark, advantage_text)
            advantage_item.arrange(RIGHT, buff=0.18)
            advantage_item.move_to(title_text.get_center() + DOWN * y_offset)
            content_group.add(advantage_item)
            y_offset += 0.3
    
    
    # 分割线
    divider2 = _create_section_divider(width * 0.75)
    divider2.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(divider2)
    y_offset += 0.5
    
    # 主要劣势 - 使用艺术字标题
    disadvantages_title = _create_artistic_title("主要劣势", font_size=22)
    disadvantages_title.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(disadvantages_title)
    y_offset += 0.45
    
    # 解析劣势列表
    disadvantages = content_data.get('劣势', '').split('，')
    for disadvantage in disadvantages:
        if disadvantage.strip():
            disadvantage_item = VGroup()
            crossmark = Text("✗", font_size=17, color=RED, weight=BOLD)
            disadvantage_text = Text(
                disadvantage.strip(),
                font_size=16,
                color=WHITE
            )
            disadvantage_item.add(crossmark, disadvantage_text)
            disadvantage_item.arrange(RIGHT, buff=0.18)
            disadvantage_item.move_to(title_text.get_center() + DOWN * y_offset)
            content_group.add(disadvantage_item)
            y_offset += 0.3
    
    
    # 分割线
    divider3 = _create_section_divider(width * 0.75)
    divider3.move_to(title_text.get_center() + DOWN * y_offset)
    content_group.add(divider3)
    y_offset += 0.5
    
    # 共同应用场景
    if content_data.get("适用场景"):
        # 使用艺术字标题
        overlap_title = _create_artistic_title("共同应用场景", font_size=20)
        overlap_title.move_to(title_text.get_center() + DOWN * y_offset)
        content_group.add(overlap_title)
        y_offset += 0.45
        
        scenarios = content_data.get('适用场景', '').split('，')
        for scenario in scenarios:
            if scenario.strip():
                scenario_item = VGroup()
                checkmark = Text("✓", font_size=15, color=BLUE, weight=BOLD)
                scenario_text = Text(
                    scenario.strip(),
                    font_size=15,
                    color=WHITE
                )
                scenario_item.add(checkmark, scenario_text)
                scenario_item.arrange(RIGHT, buff=0.18)
                scenario_item.move_to(title_text.get_center() + DOWN * y_offset)
                content_group.add(scenario_item)
                y_offset += 0.28
    
    # 智能缩放以适应面板，但保持合理的最小尺寸
    content_bottom = title_text.get_center()[1] - y_offset
    panel_bottom = panel_bg.get_bottom()[1] + 0.3  # 保留底部边距
    
    if content_bottom < panel_bottom:
        # 计算需要的缩放比例
        available_height = panel_bg.get_height() - 1.0  # 减去标题和边距
        current_content_height = y_offset
        scale_factor = min(available_height / current_content_height, 1.0)
        
        # 只在缩放比例不会太小时才缩放
        if scale_factor > 0.75:
            content_group.scale(scale_factor)
        else:
            # 如果需要缩放太多，则保持原大小但调整位置
            content_group.scale(0.85)
        
        # 重新居中内容
        content_center_y = (title_text.get_bottom()[1] + panel_bg.get_bottom()[1] + 0.3) / 2
        content_group.move_to([title_text.get_center()[0], content_center_y, 0])
    
    panel_group.add(panel_bg, title_text, content_group)
    return panel_group


def _create_vs_indicator() -> VGroup:
    """创建VS指示器，参考HTML设计"""
    vs_circle = Circle(
        radius=0.8, 
        fill_color="#ffecd2", 
        fill_opacity=1.0, 
        stroke_color=WHITE, 
        stroke_width=4
    )
    vs_text = Text("VS", font_size=48, color="#333333", weight=BOLD)
    vs_group = VGroup(vs_circle, vs_text)
    return vs_group


def _create_radar_chart_with_info(analysis_data: Dict[str, Any]) -> VGroup:
    """创建雷达图（左侧）和信息面板（右侧）"""
    full_group = VGroup()
    
    # 左侧雷达图区域中心点 - 进一步左移
    radar_center = np.array([-7, -0.3, 0])  # 从-6.0进一步左移到-6.5
    radius = 2.2  # 再稍微缩小雷达图半径
    
    # 维度标签（从维度对比分析中提取）
    dimensions = list(analysis_data.get("维度对比分析", {}).keys())
    if not dimensions:
        dimensions = ["功能特性", "编程能力", "输出格式", "易用性", "扩展性"]
    
    num_dimensions = len(dimensions)
    
    # 创建雷达图基础结构 - 使用正N边形
    radar_group = VGroup()
    
    # 创建同心正N边形
    for level in range(1, 6):
        level_radius = radius * level / 5
        polygon_points = []
        for i in range(num_dimensions):
            angle = i * 2 * PI / num_dimensions - PI / 2  # 从顶部开始
            point = radar_center + level_radius * np.array([np.cos(angle), np.sin(angle), 0])
            polygon_points.append(point)
        
        if len(polygon_points) > 2:
            level_polygon = Polygon(*polygon_points, color=ThemeUtils.get_color("text_secondary"), stroke_width=1, fill_opacity=0)
            radar_group.add(level_polygon)
    
    # 创建轴线和标签
    for i, dimension in enumerate(dimensions):
        angle = i * 2 * PI / num_dimensions - PI / 2  # 从顶部开始
        
        # 轴线
        end_point = radar_center + radius * np.array([np.cos(angle), np.sin(angle), 0])
        axis_line = Line(radar_center, end_point, color=ThemeUtils.get_color("text_secondary"), stroke_width=1)
        radar_group.add(axis_line)
        
        # 标签 - 下移并进一步增大字体
        label_pos = radar_center + (radius + 0.8) * np.array([np.cos(angle), np.sin(angle), 0])
        label = Text(dimension, font_size=26, color=ThemeUtils.get_color("text_primary"), weight=BOLD, font=ThemeUtils.get_font("primary"))  # 从24增大到26
        label.move_to(label_pos)
        radar_group.add(label)
    
    # 只显示当前方案的数据
    dimension_texts = analysis_data.get("维度对比分析", {})
    
    # 智能评分系统：基于维度分析文本内容
    def calculate_score(dimension_text: str) -> float:
        """基于文本内容计算评分"""
        positive_keywords = ["强", "高", "好", "优", "佳", "强大", "丰富", "完善", "领先"]
        negative_keywords = ["弱", "低", "差", "劣", "限制", "不足", "缺乏", "困难"]
        
        positive_count = sum(1 for keyword in positive_keywords if keyword in dimension_text)
        negative_count = sum(1 for keyword in negative_keywords if keyword in dimension_text)
        
        base_score = 3.5
        score_adjustment = (positive_count - negative_count) * 0.3
        return max(2.0, min(5.0, base_score + score_adjustment))
    
    # 创建当前方案的数据多边形
    current_points = []
    scores = []
    for i, dimension in enumerate(dimensions):
        angle = i * 2 * PI / num_dimensions - PI / 2
        dimension_text = dimension_texts.get(dimension, "")
        score = calculate_score(dimension_text)
        scores.append(score)
        point = radar_center + (radius * score / 5) * np.array([np.cos(angle), np.sin(angle), 0])
        current_points.append(point)
    
    if len(current_points) > 2:
        current_points.append(current_points[0])  # 闭合多边形
        current_polygon = Polygon(
            *current_points, 
            color=ThemeUtils.get_color("primary"), 
            fill_color=ThemeUtils.get_color("primary"),
            fill_opacity=0.3, 
            stroke_width=3
        )
        radar_group.add(current_polygon)
        
        # 添加数据点
        for point in current_points[:-1]:  # 不包括重复的闭合点
            dot = Dot(point, color=ThemeUtils.get_color("primary"), radius=0.12)
            radar_group.add(dot)
    
    # 右侧信息面板 - 重新定位，避免与底部重叠
    info_panel = VGroup()
    
    # 计算雷达图最上面的Y坐标
    radar_top_y = radar_center[1] + radius  # 雷达图标签的最高点，适应新的外移距离
    info_start_x = -3  # 右侧信息面板起始X坐标，从1.5左移到-1.5
    
    # 维度分析内容，标题和内容分行显示，确保对齐
    y_position = radar_top_y
    dimension_scores = list(zip(dimensions, scores))
    
    for dimension, score in dimension_scores:
        analysis_text = dimension_texts.get(dimension, "")
        if analysis_text.strip():  # 只显示有内容的分析
            # 维度名称和得分 - 放大字体
            dimension_label = Text(
                f"{dimension} ({score:.1f}/5.0):",
                font_size=26,  # 从18增大到20
                color=ThemeUtils.get_color("accent"),
                weight=BOLD,
                font=ThemeUtils.get_font("primary")
            )
            dimension_label.move_to(np.array([info_start_x, y_position, 0]))
            dimension_label.align_to(np.array([info_start_x, y_position, 0]), LEFT)
            
            # 分析内容 - 放大字体
            analysis_content = Text(
                analysis_text,
                font_size=23,  # 从16增大到18
                color=ThemeUtils.get_color("text_primary"),
                font=ThemeUtils.get_font("primary")
            )
            analysis_content.next_to(dimension_label, DOWN, buff=0.15)
            analysis_content.align_to(dimension_label, LEFT)
            
            dimension_item = VGroup(dimension_label, analysis_content)
            info_panel.add(dimension_item)
            
            y_position -= 1.2  # 稍微增加行间距
    
    full_group.add(radar_group, info_panel)
    return full_group


def _create_radar_chart(analysis_data: Dict[str, Any]) -> VGroup:
    """创建雷达图（保持向后兼容）"""
    return _create_radar_chart_with_info(analysis_data)


def _create_conclusion_panel(evaluation_data: Dict[str, str]) -> VGroup:
    """创建高端结论面板"""
    conclusion_group = VGroup()
    
    # 主标题 - 使用渐变效果和更大字体
    title = Text(
        "综合评估报告",
        font_size=56,
        color=GOLD,
        weight=BOLD
    )
    # 添加标题下划线装饰
    title_underline = Line(
        start=LEFT * (title.width / 2 + 0.5),
        end=RIGHT * (title.width / 2 + 0.5),
        color=GOLD,
        stroke_width=3
    )
    title_underline.next_to(title, DOWN, buff=0.2)
    
    title_group = VGroup(title, title_underline)
    conclusion_group.add(title_group)
    
    # 推荐指数 - 上移并缩小字体
    rating = evaluation_data.get("推荐指数", "4星")
    stars_bg = RoundedRectangle(
        width=3.5,  # 稍微缩小宽度
        height=0.7,  # 稍微缩小高度
        corner_radius=0.2,
        fill_color=GOLD,
        fill_opacity=0.3,
        stroke_color=GOLD,
        stroke_width=2,
        stroke_opacity=0.8
    )
    stars_bg.move_to(np.array([0, -2.3, 0]))  # 从-2.8上移到-2.3
    
    stars_text = Text(
        f"推荐指数: {rating}",
        font_size=24,  # 从28缩小到24
        color=GOLD,
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    stars_text.move_to(np.array([0, -2.3, 0]))  # 与背景重合
    
    # 装饰性星形
    star1 = Text("★", font_size=20, color=GOLD)  # 从24缩小到20
    star2 = Text("★", font_size=20, color=GOLD)  # 从24缩小到20
    star1.move_to(np.array([-2.0, -2.3, 0]))  # 对应调整位置
    star2.move_to(np.array([2.0, -2.3, 0]))   # 对应调整位置
    
    stars_group = VGroup(stars_bg, stars_text, star1, star2)
    conclusion_group.add(stars_group)
    
    # 关键结论 - 上移并合并为一个元素，保持字体颜色
    conclusion_text = Text(
        f"关键结论：{evaluation_data.get('关键结论', '')}",
        font_size=22,
        color=ThemeUtils.get_color("text_primary"),
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    # 设置标签部分的颜色
    conclusion_text[:4].set_color(ThemeUtils.get_color("accent"))  # "关键结论："部分
    conclusion_text.move_to(np.array([0, -2.9, 0]))  # 上移到-2.9，居中显示
    conclusion_group.add(conclusion_text)
    
    # 决策建议 - 上移并合并为一个元素，保持字体颜色
    recommendation_text = Text(
        f"决策建议：{evaluation_data.get('决策建议', '')}",
        font_size=22,
        color=ThemeUtils.get_color("text_primary"),
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    # 设置标签部分的颜色
    recommendation_text[:4].set_color(ThemeUtils.get_color("accent"))  # "决策建议："部分
    recommendation_text.move_to(np.array([0, -3.4, 0]))  # 上移到-3.4，居中显示
    conclusion_group.add(recommendation_text)
    
    return conclusion_group


def _create_combined_analysis_panel(analysis_data: Dict[str, Any], evaluation_data: Dict[str, str]) -> VGroup:
    """创建合并的雷达图分析和结论面板"""
    combined_group = VGroup()
    
    # 上半部分：雷达图和信息面板
    radar_chart = _create_radar_chart_with_info(analysis_data)
    radar_chart.scale(0.8)
    radar_chart.move_to(np.array([0, 0.5, 0]))  # 使用全局坐标
    combined_group.add(radar_chart)
    
    # 下半部分：结论面板 - 重新布局避免重叠
    conclusion_group = VGroup()
    
    # 推荐指数 - 上移并缩小字体
    rating = evaluation_data.get("推荐指数", "4星")
    stars_bg = RoundedRectangle(
        width=3.5,  # 稍微缩小宽度
        height=0.7,  # 稍微缩小高度
        corner_radius=0.2,
        fill_color=GOLD,
        fill_opacity=0.3,
        stroke_color=GOLD,
        stroke_width=2,
        stroke_opacity=0.8
    )
    stars_bg.move_to(np.array([0, -2.3, 0]))  # 从-2.8上移到-2.3
    
    stars_text = Text(
        f"推荐指数: {rating}",
        font_size=24,  # 从28缩小到24
        color=GOLD,
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    stars_text.move_to(np.array([0, -2.3, 0]))  # 与背景重合
    
    # 装饰性星形
    star1 = Text("★", font_size=20, color=GOLD)  # 从24缩小到20
    star2 = Text("★", font_size=20, color=GOLD)  # 从24缩小到20
    star1.move_to(np.array([-2.0, -2.3, 0]))  # 对应调整位置
    star2.move_to(np.array([2.0, -2.3, 0]))   # 对应调整位置
    
    stars_group = VGroup(stars_bg, stars_text, star1, star2)
    conclusion_group.add(stars_group)
    
    # 关键结论 - 上移并合并为一个元素，保持字体颜色
    conclusion_text = Text(
        f"关键结论：{evaluation_data.get('关键结论', '')}",
        font_size=22,
        color=ThemeUtils.get_color("text_primary"),
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    # 设置标签部分的颜色
    conclusion_text[:4].set_color(ThemeUtils.get_color("accent"))  # "关键结论："部分
    conclusion_text.move_to(np.array([0, -2.9, 0]))  # 上移到-2.9，居中显示
    conclusion_group.add(conclusion_text)
    
    # 决策建议 - 上移并合并为一个元素，保持字体颜色
    recommendation_text = Text(
        f"决策建议：{evaluation_data.get('决策建议', '')}",
        font_size=22,
        color=ThemeUtils.get_color("text_primary"),
        weight=BOLD,
        font=ThemeUtils.get_font("primary")
    )
    # 设置标签部分的颜色
    recommendation_text[:4].set_color(ThemeUtils.get_color("accent"))  # "决策建议："部分
    recommendation_text.move_to(np.array([0, -3.4, 0]))  # 上移到-3.4，居中显示
    conclusion_group.add(recommendation_text)
    
    combined_group.add(conclusion_group)
    
    return combined_group


def animate_competitive_analysis(
    scene: "FeynmanScene",
    analysis_data: Dict[str, Any],
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    创建多阶段的竞品对比分析动画
    
    Args:
        scene: Manim场景实例
        analysis_data: 包含分析数据的字典
        narration: 整体分析的语音旁白
        id: 动画的唯一标识符
    """
    logger.info(f"开始竞品对比分析动画: {id or 'competitive_analysis'}")
    scene.clear_current_mobj()
    
    # 添加背景（在整个动画开始时添加一次）
    scene.add_background()
    
    # 生成唯一ID
    content_hash = abs(hash(str(analysis_data) + (id or ""))) % 10000
    unique_id = id or f"competitive_analysis_{content_hash}"
    
    # 获取数据
    current_solution = analysis_data.get("当前方案", {})
    alternatives = analysis_data.get("替代方案", [])
    dimension_analysis = analysis_data.get("维度对比分析", {})
    evaluation = analysis_data.get("综合评估", {})
    
    # 预定义渐变色方案（第三组配色适中深度优化）
    left_gradients = [
        ["#667eea", "#764ba2"],  # 蓝紫渐变
        ["#3182ce", "#0ea5e9"],  # 深蓝色渐变（调整让白色字体更明显）
        ["#e53e3e", "#d69e2e"]   # 红橙渐变（适中深度）
    ]
    
    right_gradients = [
        ["#e91e63", "#ad1457"],  # 深粉红渐变（调整让白色字体更明显）
        ["#d63384", "#e67e22"],  # 深粉橙渐变（调整第二个对比，让白色字体更明显）
        ["#805ad5", "#3182ce"]   # 紫蓝渐变（适中深度）
    ]
    
    with scene.voiceover(text=narration) as tracker:
        # 阶段1: 1v1对比展示 - 使用Transform切换
        left_panel = None
        right_panel = None
        vs_indicator = None
        
        for i, alternative in enumerate(alternatives):
            # 创建新的面板
            new_left_panel = _create_comparison_panel(
                f"当前方案：{current_solution.get('名称', '当前方案')}",
                current_solution,
                LEFT * 3.5,
                6, 7,
                left_gradients[i % len(left_gradients)],
                is_current=True
            )
            
            new_right_panel = _create_comparison_panel(
                f"替代方案{i+1}：{alternative.get('名称', f'方案{i+1}')}",
                alternative,
                RIGHT * 3.5,
                6, 7,
                right_gradients[i % len(right_gradients)]
            )
            
            new_vs_indicator = _create_vs_indicator()
            
            if i == 0:
                # 第一次显示：直接FadeIn
                scene.play(
                    FadeIn(new_left_panel, shift=LEFT),
                    FadeIn(new_right_panel, shift=RIGHT),
                    run_time=1.5
                )
                scene.play(GrowFromCenter(new_vs_indicator), run_time=0.8)
                scene.wait(2)
                
                left_panel = new_left_panel
                right_panel = new_right_panel
                vs_indicator = new_vs_indicator
            else:
                # 后续切换：使用Transform
                scene.play(
                    Transform(left_panel, new_left_panel),
                    Transform(right_panel, new_right_panel),
                    run_time=1.2
                )
                scene.wait(2)
        
        # 阶段2: 合并的雷达图分析和综合评估
        # 1v1对比的所有元素一起退场
        comparison_elements = VGroup()
        if left_panel:
            comparison_elements.add(left_panel)
        if right_panel:
            comparison_elements.add(right_panel)
        if vs_indicator:
            comparison_elements.add(vs_indicator)
        
        if len(comparison_elements) > 0:
            scene.play(FadeOut(comparison_elements), run_time=1.0)
        
        # 创建标题 (像素范围: y=350 到 y=400)
        analysis_title = Text("维度分析与综合评估", font_size=40, color=ThemeUtils.get_color("text_primary"), weight=BOLD, font=ThemeUtils.get_font("primary"))
        analysis_title.to_edge(UP, buff=0.3)
        
        # 创建合并的分析面板 (像素范围: y=-350 到 y=300)
        combined_panel = _create_combined_analysis_panel(analysis_data, evaluation)
        
        scene.play(Write(analysis_title), run_time=1)
        scene.play(Create(combined_panel), run_time=3)
        scene.wait(3)
        
        # 最终效果
        scene.play(
            combined_panel.animate.scale(1.05),
            rate_func=there_and_back,
            run_time=1
        )
        
        # 等待旁白完成 - 修复时间计算
        # 详细计算实际动画时长：
        # 第一个方案：FadeIn(1.5) + GrowFromCenter(0.8) + wait(2) = 4.3秒
        # 后续方案：Transform(1.2) + wait(2) = 3.2秒每个
        # 退场：FadeOut(1.0)
        # 雷达图阶段：Write标题(1) + Create面板(3) + wait(3) + 最终效果(1) = 8秒
        
        first_comparison_time = 4.3  # 第一个对比的时间
        additional_comparisons_time = (len(alternatives) - 1) * 3.2 if len(alternatives) > 1 else 0
        transition_time = 1.0  # 退场时间
        radar_analysis_time = 8.0  # 雷达图分析阶段
        
        estimated_animation_time = first_comparison_time + additional_comparisons_time + transition_time + radar_analysis_time
        
        narration_duration = tracker.duration if hasattr(tracker, "duration") else 0
        remaining_time = max(0, narration_duration - estimated_animation_time)
        if remaining_time > 0:
            scene.wait(remaining_time)
    
    scene.current_mobj = combined_panel
    logger.info(f"竞品对比分析动画完成: {unique_id}") 