"""
effect: |
    按顺序高亮一系列元素，或者对代码对象高亮特定行。

use_cases:
    - 逐步引导观众注意场景中的特定对象
    - 逐行解释代码片段，高亮当前讨论的行
    - 强调流程图或架构图中的特定组件序列

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  elements:
    type: list[str]
    desc: 要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID
    required: true
  highlight_type:
    type: str
    desc: 高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化）
    default: box
  color:
    type: str
    desc: 高亮效果的颜色（十六进制或颜色名称）
    default: "#FFFF00"
  duration_per_item:
    type: float
    desc: 每个元素或代码行组的高亮持续时间（秒）
    default: 1.0
  lines:
    type: str
    desc: 要高亮的代码行范围，格式如"1-3,5,7-10"。如果提供此参数，elements应只有一个Code对象ID
    default: None
  narration:
    type: str
    desc: 播放动画时同步播放的语音旁白文本
    default: None

dsl_examples:
  - type: animate_highlight_content
    params:
      title: 重点内容高亮
      elements: ["text_obj1", "shape_obj2"]
      highlight_type: flash
      color: RED
      duration_per_item: 0.5
      narration: 首先看左边的文本，然后看右边的形状。
  - type: animate_highlight_content
    params:
      title: 代码解析
      elements: ["code_block"]
      lines: "1-2,3"
      highlight_type: box
      color: GREEN
      duration_per_item: 1.5
      narration: 现在我们来看这段代码。首先是函数定义，然后是注释。

notes:
  - 被高亮的元素必须已经在场景中存在并且有指定的ID
  - 对于代码高亮，lines参数优先于highlight_type
  - 高亮效果是暂时的，结束后元素会恢复原始状态
"""

from typing import TYPE_CHECKING, Literal, Optional

from loguru import logger
from manim import *

# Conditionally import FeynmanScene for type checking only
if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


def parse_line_ranges(line_ranges_str: str) -> list[tuple[int, int]]:
    """Parse a line range string like "1-3,5,7-10" into [(1,3), (5,5), (7,10)].

    Args:
        line_ranges_str: String representing line ranges

    Returns:
        List of tuples representing line ranges (start, end)
    """
    if not line_ranges_str:
        return []

    ranges = []
    for part in line_ranges_str.split(","):
        part = part.strip()
        if "-" in part:
            start, end = part.split("-")
            ranges.append((int(start.strip()), int(end.strip())))
        else:
            line_num = int(part.strip())
            ranges.append((line_num, line_num))
    return ranges


def _highlight_element(
    scene: "FeynmanScene",
    element: Mobject,
    highlight_type: str,
    color: str,
    duration: float,
) -> None:
    """Apply a highlight effect to a single element.

    Args:
        scene: The FeynmanScene instance
        element: The Mobject to highlight
        highlight_type: Type of highlight ('flash', 'box', 'underline', or 'color')
        color: Color to use for the highlight
        duration: Duration of the highlight effect
    """
    if highlight_type == "flash":
        scene.play(Flash(element, color=color), run_time=duration)

    elif highlight_type == "box":
        highlight_box = SurroundingRectangle(element, color=color)
        scene.play(Create(highlight_box), run_time=duration / 2)
        scene.wait(duration / 2)
        scene.play(FadeOut(highlight_box))

    elif highlight_type == "underline":
        underline = Underline(element, color=color)
        scene.play(Create(underline), run_time=duration / 2)
        scene.wait(duration / 2)
        scene.play(FadeOut(underline))

    elif highlight_type == "color":
        original_color = element.get_color()
        scene.play(element.animate.set_color(color), run_time=duration / 2)
        scene.wait(duration / 2)
        scene.play(element.animate.set_color(original_color))


def _highlight_code_lines(
    scene: "FeynmanScene",
    code_element: Code,
    line_ranges: list[tuple[int, int]],
    color: str,
    duration: float,
) -> None:
    """Highlight specific lines in a code element.

    Args:
        scene: The FeynmanScene instance
        code_element: The Code object containing the lines to highlight
        line_ranges: List of line ranges to highlight [(start, end), ...]
        color: Color to use for the highlight
        duration: Duration of the highlight effect
    """
    code_lines = code_element.code_lines

    if len(code_lines) == 0:
        logger.warning("Code element has no lines to highlight")
        return

    # Create highlight rectangles for each line range
    highlight_anims = []
    rectangles = []

    for start, end in line_ranges:
        # Find the top and bottom y-coordinates for the line range
        top_y, bottom_y = None, None

        for line_idx in range(len(code_lines)):
            if start <= line_idx + 1 <= end and line_idx < len(code_lines):
                for submob in code_lines[line_idx].submobjects:
                    if submob.height == 0:
                        continue
                    top_y = max(submob.get_top()[1], top_y) if top_y is not None else submob.get_top()[1]
                    bottom_y = min(submob.get_bottom()[1], bottom_y) if bottom_y is not None else submob.get_bottom()[1]

        if top_y is not None and bottom_y is not None:
            height, mid_y = top_y - bottom_y, (top_y + bottom_y) / 2
            rect = Rectangle(height=height, width=code_element.width, color=color)
            rect.move_to([0, mid_y, 0]).stretch_to_fit_width(code_element.width).align_to(code_element, LEFT)
            rect.set_y(mid_y).set_fill(color, opacity=0.3).set_stroke(width=0)
            rectangles.append(rect)
            highlight_anims.append(FadeIn(rect))

    # Play the highlight animations
    if highlight_anims:
        scene.play(*highlight_anims, run_time=0.5)
        scene.wait(duration)
        scene.play(*[FadeOut(rect) for rect in rectangles], run_time=0.5)


def animate_highlight_content(
    scene: "FeynmanScene",
    title: str,
    elements: list[str],
    highlight_type: Literal["flash", "box", "underline", "color"] = "box",
    color: str = "#FFFF00",
    duration_per_item: float = 1.0,
    lines: Optional[str] = None,
    narration: Optional[str] = None,
) -> None:
    logger.info(f"Highlighting content of {len(elements)} elements with {highlight_type} effect")

    # Create title object
    from dsl.v2.themes.theme_utils import ThemeUtils
    title_text = Text(title, font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
                     color=ThemeUtils.get_color("text_primary", WHITE),
                     font_size=ThemeUtils.get_font_size("h1"))

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text)

    # Parse line ranges if provided
    line_ranges = []
    if lines:
        line_ranges = parse_line_ranges(lines)
        logger.debug(f"Parsed line ranges: {lines} -> {line_ranges}")

    # Animate the sequence with or without narration
    with scene.voiceover(text=narration) as tracker:  # noqa
        # Handle animation section for transition support
        if scene.transition_enabled:
            # Title is already added by transition
            pass
        else:
            # Normal animation - show title first
            title_text.to_edge(UP, buff=0.8)
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)
        for i, element_id in enumerate(elements):
            element_attr = element_id

            if hasattr(scene, element_attr):
                element = getattr(scene, element_attr)
                logger.info(f"Highlighting element: {element_id}")

                # Check if we need to highlight code lines
                # 贪心找第一个Code
                if line_ranges:
                    objs = [element]
                    while objs:
                        target = objs.pop()
                        if isinstance(target, Code):
                            element = target
                            break
                        else:
                            for submob in target.submobjects:
                                objs.append(submob)
                    if line_ranges and isinstance(element, Code):
                        _highlight_code_lines(scene, element, line_ranges, color, duration_per_item)
                    else:
                        _highlight_element(scene, element, highlight_type, color, duration_per_item)
                else:
                    _highlight_element(scene, element, highlight_type, color, duration_per_item)
            else:
                logger.warning(f"Element with ID '{element_id}' not found, cannot highlight")

            # Add a brief pause between elements (except after the last one)
            if i < len(elements) - 1:
                scene.wait(0.5)

    # Create display group with title and existing content
    # Since this is a highlighting function, we keep the existing scene content
    all_elements = [getattr(scene, element_id) for element_id in elements if hasattr(scene, element_id)]
    if all_elements:
        display_group = Group(title_text, *all_elements)
        display_group.arrange(DOWN, buff=0.5)

        # Update current_mobject saving - ensure title is first submobject
        scene.current_mobj = display_group
        scene.save_scene_state("highlight_content", f"highlight_{len(elements)}_elements")

    logger.info("Completed highlight content")
