"""
effect: |
  在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。
  可以自定义起始值、目标值、标签、单位、动画时长和结束特效。

use_cases:
  - 显示随时间变化的数值，如统计数据、得分、加载进度等
  - 以曲线图形式展示增长或变化趋势，并在终点显示目标值
  - 强调关键性能指标(KPI)的变化

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  target_value:
    type: float
    desc: 计数器动画的目标值
    required: true
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  start_value:
    type: float
    desc: 计数器的起始值。仅用于'counter'类型
    default: 0
  counter_type:
    type: str
    desc: 计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图）
    default: counter
  label:
    type: str
    desc: 计数器的标签或标题文本
    default: None
  unit:
    type: str
    desc: 计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端
    default: None
  duration:
    type: float
    desc: 计数器动画的持续时间（秒）
    default: 2.0
  effect:
    type: str
    desc: 动画结束时应用的额外视觉效果。可选值：'flash', 'zoom'
    default: None
  narration:
    type: str
    desc: 播放动画时同步播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_counter
    params:
      counter_type: counter
      target_value: 100
      title: 进度统计
      label: 进度
      unit: '%'
      duration: 3
      effect: flash
      narration: 加载进度达到100%。
  - type: animate_counter
    params:
      counter_type: curve
      target_value: 500
      title: 用户增长
      label: 用户增长曲线
      unit: 用户
      duration: 4
      narration: 用户数量快速增长至500。

notes:
  - counter类型显示一个从起始值到目标值的数字动画
  - curve类型显示一条增长曲线，终点显示目标值
  - 结束特效在动画完成后应用，可增强视觉效果
"""

from typing import TYPE_CHECKING, Optional

from loguru import logger
from manim import *

from dsl.v2.themes.theme_utils import ThemeUtils

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

custom_xelatex_template = TexTemplate(
    tex_compiler="xelatex",
    description="LXGW",
    output_format=".xdv",
    documentclass="\\documentclass[preview]{standalone}",
    preamble="\n\\usepackage[english]{babel}\n\\usepackage{amsmath}\n\\usepackage{amssymb}\n\n\n\\usepackage{txfonts}\n\\usepackage[no-math]{fontspec}\n\\setmainfont[Mapping=tex-text]{LXGW WenKai Mono}\n\\usepackage[defaultmathsizes]{mathastext}\n",
    placeholder_text="YourTextHere",
    post_doc_commands="",
)


def _create_numeric_counter(
    start_value: float,
    target_value: float,
    label: Optional[str] = None,
    unit: Optional[str] = None,
) -> VGroup:
    """Create a numeric counter with optional label and unit.

    Args:
        scene: The FeynmanScene instance
        start_value: Initial value of the counter
        target_value: Target value for the counter animation
        label: Optional label text to display before the counter
        unit: Optional unit text to display after the counter

    Returns:
        VGroup containing the complete counter with label and unit
    """
    # Calculate decimal places - integers don't show decimal places
    decimal_places = 0
    if target_value % 1 == 0:  # Check if integer
        decimal_places = 0
    else:
        target_str = str(target_value)
        if "." in target_str:
            decimal_places = len(target_str.split(".")[1])

    # Create the number display
    number = DecimalNumber(
        number=start_value,
        num_decimal_places=decimal_places,
        include_sign=False,
        group_with_commas=True,
        edge_to_fix=RIGHT,
        mob_class=MarkupText,
        font_size=ThemeUtils.get_font_size("h2", 48),
        color=ThemeUtils.get_color("text_primary"),
    )

    # Create combined object (number + optional label and unit)
    has_label = bool(label)
    has_unit = bool(unit)

    # Create label and unit objects if provided
    label_obj = None
    unit_obj = None

    if has_label:
        label_obj = Text(
            label,
            font_size=ThemeUtils.get_font_size("h2", 48),
            font=ThemeUtils.get_font("primary"),
            color=ThemeUtils.get_color("text_primary"),
        )

    if has_unit:
        unit_obj = Text(
            unit,
            font_size=ThemeUtils.get_font_size("h2", 48),
            font=ThemeUtils.get_font("primary"),
            color=ThemeUtils.get_color("text_primary"),
        )

    # Calculate dynamic spacing (only for label and number)
    target_val_int = int(abs(target_value))
    num_digits = len(str(target_val_int)) if target_val_int != 0 else 1
    dynamic_buff = ThemeUtils.get_spacing("sm", DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5) + num_digits * 0.25

    # Create counter group and arrange as needed
    group_elements = [number]
    if has_label:
        group_elements.insert(0, label_obj)  # Label comes first
    if has_unit:
        group_elements.append(unit_obj)  # Unit comes last

    counter_group = VGroup(*group_elements)

    # Arrange the elements
    if has_label and has_unit:
        label_obj.next_to(number, LEFT, buff=dynamic_buff)
        unit_obj.next_to(number, RIGHT, buff=DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)
        counter_group.arrange(RIGHT, buff=dynamic_buff)  # Arrange label-number-unit
    elif has_label:
        label_obj.next_to(number, LEFT, buff=dynamic_buff)
        counter_group.arrange(RIGHT, buff=dynamic_buff)  # Arrange label-number
    elif has_unit:
        unit_obj.next_to(number, RIGHT, buff=DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)
        counter_group.arrange(
            RIGHT, buff=ThemeUtils.get_spacing("xs", DEFAULT_MOBJECT_TO_MOBJECT_BUFFER * 0.5)
        )  # Arrange number-unit

    return counter_group, number  # Return both the group and the number object for animation


def _create_curve_graph(
    target_value: float,
    label: Optional[str] = None,
    unit: Optional[str] = None,
) -> VGroup:
    """Create a curve graph counter with optional title.

    Args:
        scene: The FeynmanScene instance
        target_value: Target value for the curve endpoint
        label: Optional title for the graph
        unit: Optional unit to display with the target value

    Returns:
        VGroup containing the complete curve graph
    """
    # Create coordinate system
    axis_color = ThemeUtils.get_color("text_secondary")
    axis_stroke_width = ThemeUtils.get_component_style("chart", "axis_stroke_width", 4)

    axes = Axes(
        x_range=[0, 6, 1],
        y_range=[0, 35, 5],
        x_length=8,
        y_length=6,
        axis_config={
            "include_numbers": False,
            "tip_shape": StealthTip,
            "include_ticks": False,
            "stroke_width": axis_stroke_width,
            "color": axis_color,
        },
        tips=True,
    )

    # Define and plot the exponential function y = x^2
    graph = axes.plot(
        lambda x: x**2,
        x_range=[0, 5, 1],
        color=ThemeUtils.get_color("accent", YELLOW),
        use_smoothing=True,
        stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 8),
    )

    # Add label at the curve endpoint
    curve_end_point = graph.points[-1]  # Get the last point of the curve
    value_text = f"{target_value}{unit or ''}"
    value_label = Text(
        value_text,
        font_size=ThemeUtils.get_font_size("h2", 48),
        font=ThemeUtils.get_font("primary"),
        color=ThemeUtils.get_color("text_primary"),
    )
    value_label.next_to(curve_end_point, UR, buff=ThemeUtils.get_spacing("xs", 0.2))

    # Add title if provided
    title = None
    if label:
        title = Text(
            label,
            font_size=ThemeUtils.get_font_size("h3", 36),
            font=ThemeUtils.get_font("primary"),
            color=ThemeUtils.get_color("text_primary"),
        )
        title.next_to(axes, UP, buff=ThemeUtils.get_spacing("xs", 0.2))
        return VGroup(title, axes, graph, value_label), graph, value_label
    else:
        return VGroup(axes, graph, value_label), graph, value_label


def animate_counter(
    scene: "FeynmanScene",
    target_value: float,
    title: str,
    start_value: float = 0,
    counter_type: str = "counter",  # 'counter' or 'curve'
    label: Optional[str] = None,
    unit: Optional[str] = None,
    duration: float = 2.0,
    effect: Optional[str] = None,  # 'flash' or 'zoom'
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    logger.info(f"Animating {counter_type} counter...")

    # Generate a unique ID if not provided
    label_str = str(label) if label is not None else ""
    unit_str = str(unit) if unit is not None else ""
    value_str = str(target_value)
    content_hash = abs(hash(value_str + label_str + unit_str + counter_type + (id or ""))) % 10000
    unique_id = id or f"{counter_type}_{content_hash}"

    # Create title object
    title_text = Text(title, font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
                     color=ThemeUtils.get_color("text_primary", WHITE),
                     font_size=ThemeUtils.get_font_size("h1"))

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text)

    # Get target region rectangle for positioning
    target_rect = scene.full_screen_rect

    # Create counter based on type
    if counter_type == "counter":
        # Create numeric counter
        counter_group, number_obj = _create_numeric_counter(
            start_value=start_value,
            target_value=target_value,
            label=label,
            unit=unit,
        )

        # Create display group with title and counter content
        display_group = Group(title_text, counter_group)
        display_group.arrange(DOWN, buff=0.5)

        # Position and scale display group to fit target region
        if target_rect:
            display_group.move_to(target_rect.get_center())

            # Scale if necessary to fit the region
            if display_group.width > 0 and display_group.height > 0:
                available_width = target_rect.width * 0.8
                available_height = target_rect.height * 0.8
                scale_factor = min(
                    available_width / display_group.width if display_group.width > 0 else 1.0,
                    available_height / display_group.height if display_group.height > 0 else 1.0,
                )
                if scale_factor < 1.0:
                    display_group.scale(scale_factor)

        with scene.voiceover(text=narration) as tracker:
            # Handle animation section for transition support
            if not scene.transition_enabled:
                # Normal animation - show title first
                scene.play(Write(title_text), run_time=1.0)
                scene.wait(0.5)

            # Always animate the counter content with original effects
            scene.play(Create(counter_group))
            scene.wait(0.5)  # Brief pause to let viewers see the initial value

            # Animate the counter value change
            scene.play(
                ChangeDecimalToValue(number_obj, target_value),
                run_time=ThemeUtils.get_animation_duration("counter_change", duration),
            )

            # Add ending effect if specified
            if effect == "flash":
                scene.play(
                    Flash(
                        number_obj,
                        color=ThemeUtils.get_color("highlight", WHITE),
                        flash_radius=ThemeUtils.get_component_style("counter", "flash_radius", 0.5),
                    )
                )
            elif effect == "zoom":
                scene.play(
                    number_obj.animate.scale(ThemeUtils.get_component_style("counter", "zoom_scale", 1.2)),
                    rate_func=there_and_back,
                    run_time=ThemeUtils.get_animation_duration("zoom", 0.5),
                )

            # Wait for narration to complete if needed
            narration_duration = tracker.duration if hasattr(tracker, "duration") else 0
            effective_duration = max(0, narration_duration - duration - 1.0)
            if effective_duration > 0:
                scene.wait(effective_duration)

        # Update current_mobject saving - ensure title is first submobject
        scene.current_mobj = display_group
        scene.save_scene_state("counter", unique_id)

    elif counter_type == "curve":
        # Create curve graph
        curve_group, graph_obj, value_label = _create_curve_graph(
            target_value=target_value,
            label=label,
            unit=unit,
        )

        # Create display group with title and curve content
        display_group = Group(title_text, curve_group)
        display_group.arrange(DOWN, buff=0.5)

        # Position and scale display group to fit target region
        if target_rect:
            display_group.move_to(target_rect.get_center())

            # Scale if necessary to fit the region
            if display_group.width > 0 and display_group.height > 0:
                available_width = target_rect.width * ThemeUtils.get_component_style("counter", "width_ratio", 0.9)
                available_height = target_rect.height * ThemeUtils.get_component_style("counter", "height_ratio", 0.9)
                scale_factor = min(
                    available_width / display_group.width if display_group.width > 0 else 1.0,
                    available_height / display_group.height if display_group.height > 0 else 1.0,
                )
                if scale_factor < 1.0:
                    display_group.scale(scale_factor)

        # Animate the curve with or without narration
        if narration:
            with scene.voiceover(text=narration) as tracker:
                # Handle animation section for transition support
                if not scene.transition_enabled:
                    # Normal animation - show title first
                    scene.play(Write(title_text), run_time=1.0)
                    scene.wait(0.5)

                # Always animate the curve content with original effects
                if label:
                    scene.play(Write(curve_group[0]))  # Title is the first element if it exists
                scene.play(Create(curve_group[1 if label else 0]))  # Axes

                # Animate curve drawing
                scene.play(Create(graph_obj), run_time=ThemeUtils.get_animation_duration("curve_draw", duration))

                # Show value label
                scene.play(Write(value_label))

                # Add ending effect
                scene.play(
                    Flash(
                        value_label,
                        color=ThemeUtils.get_color("highlight", WHITE),
                        flash_radius=ThemeUtils.get_component_style("counter", "flash_radius", 0.5),
                    )
                )

                # Wait for narration to complete if needed
                narration_duration = tracker.duration if hasattr(tracker, "duration") else 0
                effective_duration = max(0, narration_duration - duration - 2.0)
                if effective_duration > 0:
                    scene.wait(effective_duration)
        else:
            # Without narration
            # Handle animation section for transition support
            if not scene.transition_enabled:
                # Normal animation - show title first
                scene.play(Write(title_text), run_time=1.0)
                scene.wait(0.5)

            # Always animate the curve content with original effects
            if label:
                scene.play(Write(curve_group[0]))  # Title
            scene.play(Create(curve_group[1 if label else 0]))  # Axes

            scene.play(Create(graph_obj), run_time=ThemeUtils.get_animation_duration("curve_draw", duration))
            scene.play(Write(value_label))
            scene.play(
                Flash(
                    value_label,
                    color=ThemeUtils.get_color("highlight", WHITE),
                    flash_radius=ThemeUtils.get_component_style("counter", "flash_radius", 0.5),
                )
            )

            scene.wait(0.5)

        # Update current_mobject saving - ensure title is first submobject
        scene.current_mobj = display_group
        scene.save_scene_state("counter", unique_id)
    else:
        logger.error(f"Unsupported counter type: {counter_type}")
        return

    logger.info(f"Completed animating {counter_type} counter '{unique_id}' in region full_screen")
