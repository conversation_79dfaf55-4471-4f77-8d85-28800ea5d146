"""
effect: |
    创建现代化的深度洞察卡片动画，采用"Sparkling Insight Card"设计风格，支持闪光特效和优雅的悬浮动画

use_cases:
    - 深度洞察分析结果展示
    - 思维启发和认知增强
    - 学术论文的核心观点提炼
    - 商业分析的关键发现
    - 创新思维的火花展示

params:
  scene:
    type: FeynmanScene
    desc: <PERSON><PERSON>场景实例（由系统自动传入）
  insights_data:
    type: list
    desc: 深度洞察数据列表，每个元素包含insight_title和insight_description字段
    required: true
  title:
    type: str
    desc: 展示标题
    required: false
    default: "✨ 深度洞察"
  cards_per_screen:
    type: int
    desc: 每屏显示的卡片数量（建议最多3个）
    required: false
    default: 3
  duration_per_card:
    type: float
    desc: 每张卡片的展示时长（秒）
    required: false
    default: 4.0
  theme:
    type: str
    desc: 主题风格。可选值：'sparkling'（闪光）, 'elegant'（优雅）, 'modern'（现代）
    required: false
    default: sparkling
  narration:
    type: str
    desc: 语音旁白内容
    required: true

dsl_examples:
  - type: animate_deep_insight
    params:
      insights_data:
        - insight_title: "用户流失的真正原因"
          insight_description: "用户流失的真正原因，不是功能缺失，而是体验上的细微摩擦累积。"
        - insight_title: "数据驱动决策的核心"
          insight_description: "有效的数据分析不在于数据量的大小，而在于找到能直接影响业务结果的关键指标。"
        - insight_title: "创新的本质特征"
          insight_description: "真正的创新往往来自于对现有问题的重新定义，而不是对现有解决方案的改进。"
      title: "商业洞察精选"
      theme: "sparkling"
      narration: "让我们一起探索这些闪闪发光的商业洞察，每一个都可能改变我们的思维方式"
  - type: animate_deep_insight
    params:
      insights_data:
        - insight_title: "算法偏见的根源"
          insight_description: "机器学习算法的偏见不是技术问题，而是训练数据中隐含的社会偏见的放大。"
      title: "AI伦理思考"
      cards_per_screen: 1
      duration_per_card: 6.0
      theme: "elegant"
      narration: "深入思考人工智能发展过程中的伦理挑战和社会责任"

notes:
  - 完全复刻HTML版本的"Sparkling Insight Card"设计
  - 柔和的浅蓝色背景营造轻松氛围
  - 白色卡片背景配合圆角和阴影效果
  - 金色✨图标和顶部闪光条特效
  - 轻微倾斜和悬浮动画增强视觉吸引力
  - 支持智能文本换行和响应式布局
"""

import logging

from manim import *

from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions.animate_markdown import create_glow_text_simple

logger = logging.getLogger(__name__)


def animate_deep_insight(
    scene: FeynmanScene,
    insights_data: list[dict[str, str]],
    title: str = "✨ 深度洞察",
    cards_per_screen: int = None,  # 改为可选参数，如果不提供则自动计算
    duration_per_card: float = 4.0,
    theme: str = "sparkling",
    narration: str = "让我们来探索这些闪闪发光的深度洞察",
) -> None:
    """
    创建现代化的深度洞察卡片展示动画，采用Sparkling Insight Card设计风格
    自动根据洞察数量决定每屏显示的卡片数量，一次最多3个
    """
    logger.info(f"开始创建深度洞察卡片动画，共{len(insights_data)}个洞察")

    try:
        scene.clear_current_mobj()

        # 设置柔和的浅蓝色背景，模拟HTML的 #f0f7ff
        scene.camera.background_color = "#f0f7ff"

        # 自动决定每屏卡片数量，如果没有指定的话
        if cards_per_screen is None:
            total_insights = len(insights_data)
            if total_insights == 1:
                cards_per_screen = 1
            elif total_insights == 2:
                cards_per_screen = 2
            elif total_insights <= 6:
                cards_per_screen = 3  # 3-6个洞察，每屏显示3个
            else:
                cards_per_screen = 3  # 超过6个，每屏显示3个

        # 确保最多3个卡片
        cards_per_screen = min(cards_per_screen, 3)

        with scene.voiceover(narration) as tracker:  # noqa: F841
            # 创建标题 - 使用发光字体系统
            title_text = create_glow_text_simple(
                title,
                font_size=42,
            )
            title_text.to_edge(UP, buff=0.8)
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)

            # 计算需要的屏幕数
            total_screens = (len(insights_data) + cards_per_screen - 1) // cards_per_screen

            for screen_idx in range(total_screens):
                start_idx = screen_idx * cards_per_screen
                end_idx = min(start_idx + cards_per_screen, len(insights_data))
                screen_insights_data = insights_data[start_idx:end_idx]

                # 创建当前屏幕的卡片
                cards = []
                card_group = VGroup()

                for i, insight in enumerate(screen_insights_data):
                    card = create_sparkling_insight_card(
                        insight["insight_title"], insight["insight_description"], len(screen_insights_data), theme
                    )
                    cards.append(card)
                    card_group.add(card)

                # 横向排列卡片
                if len(screen_insights_data) == 1:
                    card_group.move_to(ORIGIN + DOWN * 0.3)
                else:
                    card_group.arrange(RIGHT, buff=0.4)
                    card_group.move_to(ORIGIN + DOWN * 0.3)

                    # 屏幕边界检查
                    if card_group.get_width() > 14:
                        card_group.scale_to_fit_width(14)

                # 应用闪光入场动画
                apply_sparkling_animation(scene, cards)

                # 等待展示
                scene.wait(duration_per_card)

                # 如果不是最后一屏，清除当前卡片
                if screen_idx < total_screens - 1:
                    scene.play(*[FadeOut(card) for card in cards], run_time=1.0)

            # 保存当前对象
            scene.current_mobj = VGroup(title_text, *cards)
            scene.save_scene_state("deep_insight", "deep_insight")

    except Exception as e:
        logger.error(f"创建深度洞察卡片动画时出错: {e}")
        error_text = create_glow_text_simple(
            f"深度洞察卡片创建失败\n错误: {str(e)[:50]}...", 
            font_size=24
        ).move_to(ORIGIN)
        scene.add(error_text)
        scene.current_mobj = error_text


def create_sparkling_insight_card(title: str, description: str, total: int, theme: str) -> VGroup:
    """创建Sparkling Insight Card风格的洞察卡片"""

    # 根据卡片数量调整尺寸（响应式设计）
    if total == 1:
        card_width, card_height = 6.5, 5.0  # 调小单张卡片尺寸，更协调
    elif total == 2:
        card_width, card_height = 6.5, 5.0
    else:  # 3个或更多
        card_width, card_height = 5.8, 5.0

    # 创建卡片背景 - 根据主题选择不同的背景色和效果
    if theme == "sparkling":
        bg_color = WHITE
        stroke_color = "#e2e8f0"
        shadow_color = "#FFD700"
        shadow_opacity = 0.15
    elif theme == "elegant":
        bg_color = "#f8fafc"  # 更柔和的浅色背景
        stroke_color = "#cbd5e1"
        shadow_color = "#4A69DD"
        shadow_opacity = 0.12
    else:  # modern
        bg_color = "#fefefe"
        stroke_color = "#d1fae5"
        shadow_color = "#1ABC9C"
        shadow_opacity = 0.12

    card_bg = RoundedRectangle(
        corner_radius=0.28,  # 稍微增大圆角
        width=card_width,
        height=card_height,
        fill_color=bg_color,
        fill_opacity=1.0,
        stroke_color=stroke_color,
        stroke_width=1.5,
        stroke_opacity=0.4,
    )

    # 创建多层阴影效果 - 更好看的阴影
    shadow1 = card_bg.copy()
    shadow1.set_fill(color=shadow_color, opacity=shadow_opacity)
    shadow1.set_stroke(width=0)
    shadow1.shift(DOWN * 0.06 + RIGHT * 0.01)

    shadow2 = card_bg.copy()
    shadow2.set_fill(color=shadow_color, opacity=shadow_opacity * 0.5)
    shadow2.set_stroke(width=0)
    shadow2.shift(DOWN * 0.12 + RIGHT * 0.02)

    # 创建顶部闪光条 - 模拟HTML的::before伪元素，根据主题选择颜色
    sparkle_bar = Rectangle(
        width=card_width * 0.8,
        height=0.1,  # 稍微增高
        fill_opacity=0.0,  # 初始透明
        stroke_width=0,
    )

    # 根据主题选择闪光条颜色
    if theme == "sparkling":
        sparkle_color = "#fdd835"  # 金色
    elif theme == "elegant":
        sparkle_color = "#6366f1"  # 紫色
    else:  # modern
        sparkle_color = "#10b981"  # 绿色

    # 创建渐变闪光效果
    sparkle_gradient = Rectangle(
        width=card_width * 0.8, height=0.1, fill_color=sparkle_color, fill_opacity=0.85, stroke_width=0
    )
    sparkle_bar.add(sparkle_gradient)
    sparkle_bar.move_to(card_bg.get_top() + DOWN * 0.05)
    sparkle_bar.stretch_to_fit_width(0)  # 初始宽度为0

    # 创建灵光一现图标 - 根据主题选择不同图标
    if theme == "sparkling":
        icon_text = "💡"  # 灯泡图标表示灵光一现
        icon_color = "#FFC700"  # 金色
    elif theme == "elegant":
        icon_text = "i"  # 字母i表示insight
        icon_color = "#4A69DD"  # 靛蓝色
    else:  # modern
        icon_text = "💡"
        icon_color = "#1ABC9C"  # 松石绿

    # 如果是字母i，需要特殊处理样式
    if icon_text == "i":
        # 创建带圆形背景的字母i
        circle_bg = Circle(
            radius=0.4 if total == 1 else (0.35 if total == 2 else 0.3),
            fill_color=icon_color,
            fill_opacity=0.9,
            stroke_color=WHITE,
            stroke_width=2,
        )
        icon_letter = create_glow_text_simple(
            "i",
            font_size=52 if total == 1 else (44 if total == 2 else 38),
        )
        icon = VGroup(circle_bg, icon_letter)
    else:
        icon = create_glow_text_simple(
            icon_text,
            font_size=52 if total == 1 else (44 if total == 2 else 38),  # 稍微增大图标
        )

    # 创建"灵光一现"标签 - 使用emoji图标和发光字体
    subtitle = create_glow_text_simple(
        "💡✨",
        font_size=20 if total == 1 else (18 if total == 2 else 16),
    ).set_opacity(0.7)

    # 智能换行函数
    def smart_wrap_insight_text(text: str, max_width: float, font_size: int, max_lines: int = 3) -> str:
        """为洞察内容进行智能换行"""
        char_width_ratio = font_size * 0.65
        chars_per_line = int(max_width / char_width_ratio * 1.8)
        chars_per_line = max(15, min(chars_per_line, 40))

        def smart_break(text, width):
            if len(text) <= width:
                return [text]

            lines = []
            remaining = text

            while remaining and len(lines) < max_lines - 1:
                if len(remaining) <= width:
                    lines.append(remaining)
                    remaining = ""  # 修复：将remaining置空，避免重复添加
                    break

                # 寻找合适的断点
                break_point = width
                for i in range(width, max(width // 2, 10), -1):
                    if i < len(remaining) and remaining[i] in "，。！？；：、":
                        break_point = i + 1
                        break
                    elif i < len(remaining) and remaining[i] == " ":
                        break_point = i
                        break

                lines.append(remaining[:break_point].strip())
                remaining = remaining[break_point:].strip()

            if remaining and len(lines) < max_lines:
                if len(remaining) <= width * 1.2:
                    lines.append(remaining)
                else:
                    lines.append(remaining[: width - 3] + "...")
            elif remaining:
                if lines:
                    lines[-1] = lines[-1][: width - 3] + "..."

            return lines

        wrapped_lines = smart_break(text, chars_per_line)
        return "\n".join(wrapped_lines)

    # 创建洞察标题文本 - 模拟CSS的font-size: 1.6em, font-weight: 600
    text_width = card_width - 1.6
    # 增大标题字体：单张28，两张24，多张20
    title_font_size = 28 if total == 1 else (24 if total == 2 else 20)

    wrapped_title = smart_wrap_insight_text(title, text_width, title_font_size, max_lines=2)

    title_text = create_glow_text_simple(
        wrapped_title,
        font_size=title_font_size,
        line_spacing=1.3,  # 增加行间距，让多行标题更易读
    )

    # 创建标题和正文之间的短分割线
    divider_width = text_width * 0.6  # 分割线长度为文本宽度的60%
    divider = Rectangle(
        width=divider_width,
        height=0.03,  # 细线条
        fill_color="#CBD5E1",  # 浅灰色
        fill_opacity=0.8,
        stroke_width=0,
    )

    # 创建描述文本 - 模拟CSS的较小字体和斜体
    # 增大描述字体：单张20，两张18，多张16
    desc_font_size = 20 if total == 1 else (18 if total == 2 else 16)
    wrapped_description = smart_wrap_insight_text(description, text_width, desc_font_size, max_lines=4)

    description_text = create_glow_text_simple(
        wrapped_description,
        font_size=desc_font_size,
        line_spacing=1.4,  # 增加行间距，让描述文本更易读
    )

    # 垂直排列内容 - 增加各元素之间的间距
    content_group = VGroup(icon, subtitle, title_text, divider, description_text).arrange(DOWN, buff=0.35, aligned_edge=ORIGIN)

    # 内容居中到卡片
    content_group.move_to(card_bg.get_center())

    # 创建轻微倾斜效果 - 模拟CSS的transform: rotateZ(1.5deg)
    card_container = VGroup(shadow2, shadow1, card_bg, sparkle_bar, content_group)
    card_container.rotate(1.5 * DEGREES)

    # 存储动画元素的引用
    card_container.sparkle_bar = sparkle_bar
    card_container.icon = icon

    return card_container


def apply_sparkling_animation(scene: FeynmanScene, cards: list[VGroup]) -> None:
    """应用闪光特效和入场动画"""
    for i, card in enumerate(cards):
        # 初始位置和状态
        card.shift(DOWN * 1.0)
        card.set_opacity(0)

        # 图标弹出动画 - 模拟CSS的@keyframes pop-in
        card.icon.scale(0.5)

        # 卡片入场动画
        scene.play(
            AnimationGroup(
                card.animate.shift(UP * 1.0).set_opacity(1),
                card.icon.animate.scale(2.0),  # 恢复到正常大小
                lag_ratio=0.3,
            ),
            run_time=1.0 + i * 0.2,
            rate_func=smooth,
        )

        # 悬浮效果 - 模拟CSS的hover效果
        scene.play(
            AnimationGroup(
                card.animate.rotate(-1.5 * DEGREES).shift(UP * 0.08).scale(1.02),  # 回正并轻微上浮
                card.sparkle_bar.animate.stretch_to_fit_width(card.get_width() * 0.8).set_opacity(0.9),  # 闪光条出现
                lag_ratio=0.1,
            ),
            run_time=0.6,
            rate_func=smooth,
        )

        # 轻微摆动效果
        scene.play(card.animate.rotate(0.5 * DEGREES), run_time=0.4, rate_func=there_and_back)

        scene.wait(0.2)
