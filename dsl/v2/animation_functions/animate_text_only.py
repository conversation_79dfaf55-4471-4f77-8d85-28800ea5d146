"""
effect: |
    创建纯文本内容的动态展示动画，专门用于文字信息的呈现。支持清单、要点、步骤等文本内容的动态显示，
    包含渐变背景、发光字体效果、逐字显示动画等视觉特效，适合纯文本内容的精美展示。

use_cases:
    - 展示要点清单、任务列表、检查清单等文本内容
    - 创建纯文字的动态内容展示，如学习要点、工作计划等
    - 制作带有视觉特效的文本演示，突出重要信息
    - 文字内容的分步展示和强调

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 文本展示的主标题
    default: "✨ 夏日出游 Checklist"
  items:
    type: List[Dict]
    desc: 文本项目列表，每个项目包含text、tags字段，颜色会自动分配
    default: null
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_text_only
    params:
      title: "📚 学习要点总结"
      items:
        - text: "理论知识学习"
          tags: "#基础概念 #核心原理"
        - text: "实践操作练习"
          tags: "#动手实践 #技能提升"
        - text: "目标达成检验"
          tags: "#效果评估 #持续改进"
      narration: "这是我们学习过程中需要重点关注的三个要点。"
  - type: animate_text_only
    params:
      title: "🎯 工作计划清单"
      items:
        - text: "制定详细计划"
          tags: "#时间安排 #任务分解"
        - text: "高效执行任务"
          tags: "#专注投入 #质量保证"
        - text: "跟踪进度反馈"
          tags: "#进度监控 #及时调整"
        - text: "完成总结复盘"
          tags: "#成果总结 #经验提炼"
      narration: "高效工作需要遵循这四个关键步骤。"

notes:
  - 专门用于纯文本内容的动态展示，不涉及图片或视频
  - 支持最多5个文本项目的展示
  - 每个文本项包含主文本、标签，颜色自动分配
  - 具有发光字体效果和逐字显示动画
  - 适合要点总结、清单展示、步骤说明等文本场景
  - 使用渐变背景和动态特效提升文本展示的视觉效果
"""

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

from loguru import logger
from manim import *


def animate_text_only(
    scene: "FeynmanScene",
    title: str = "✨ 夏日出游 Checklist",
    items: Optional[list[dict[str, str]]] = None,
    narration: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    """
    创建纯文本内容的动态展示动画

    专门用于文字信息的呈现，支持清单、要点、步骤等文本内容的动态显示，
    包含发光字体效果、逐字显示动画等视觉特效。

    Args:
        scene: Manim场景实例
        title: 文本展示的主标题
        items: 文本项目列表，每个项目包含text、tags字段
        narration: 语音旁白文本
        id: 唯一标识符
    """
    logger.info(f"开始创建纯文本动态展示: {title}")

    scene.clear_current_mobj()

    unique_id = id or f"text_only_{abs(hash(title)) % 10000}"

    # 默认颜色配置
    default_colors = ["#f59e0b", "#3b82f6", "#10b981", "#8b5cf6", "#06b6d4"]

    # 默认清单项目（复刻HTML中的内容）
    if items is None:
        items = [
            {"text": "智能防晒霜", "tags": "#物理防晒 #SPF50+"},
            {"text": "偏光墨镜", "tags": "#UV400 #驾驶友好"},
            {"text": "便携风扇", "tags": "#静音 #长续航"},
            {"text": "降温冰袖", "tags": "#凉感面料 #防晒"},
        ]

    # 为每个项目分配默认颜色
    for i, item in enumerate(items):
        if "color" not in item or not item["color"]:
            item["color"] = default_colors[i % len(default_colors)]

    # 创建简单背景 - 只保留一个背景框
    def create_simple_background():
        """创建简单的渐变背景"""
        bg_width = 7  # 长宽比 3:2
        bg_height = 7

        # 只保留一层背景
        background = RoundedRectangle(
            width=bg_width, height=bg_height, fill_color="#1e3c72", fill_opacity=0.9, stroke_width=0, corner_radius=0.3
        )

        return background

    # 创建主容器 - 适当留出边距
    def create_main_container():
        """创建主容器，适当留出边距"""
        container_width = 6  # 比背景稍小，留出边距
        container_height = 6  # 长宽比接近3:2

        # 主容器
        main_container = RoundedRectangle(
            width=container_width,
            height=container_height,
            corner_radius=0.4,
            fill_color=WHITE,
            fill_opacity=0.1,
            stroke_color=WHITE,
            stroke_width=1.2,
            stroke_opacity=0.2,
        )

        return main_container

    # 创建背景元素
    background = create_simple_background()
    container = create_main_container()

    # 创建标题 - 优化样式，调整位置留出顶部空隙，添加发光效果
    title_text = Text(
        title,
        font_size=32,  # 适当调整标题字体
        color=WHITE,
        weight=BOLD,
        font="Arial",
    )
    # 添加标题发光效果
    title_glow = title_text.copy()
    title_glow.set_color("#87CEEB")
    title_glow.set_stroke(color="#87CEEB", width=3, opacity=0.8)
    title_combined = VGroup(title_glow, title_text)
    title_combined.move_to(UP * 2.3)  # 调整标题位置适应新高度

    # 创建标题下的横线
    title_line = Line(start=LEFT * 2.5, end=RIGHT * 2.5, color=WHITE, stroke_width=2, stroke_opacity=0.6)
    title_line.move_to(UP * 1.9)  # 调整横线位置适应新高度

    # 创建清单项目 - 优化文字样式，调整布局
    checklist_items = []
    item_positions = [UP * 1.4, UP * 0.5, DOWN * 0.4, DOWN * 1.3, DOWN * 2.2]  # 进一步增加项目间距避免重叠

    for i, item_data in enumerate(items):
        if i >= len(item_positions):
            break

        # 项目背景容器 - 调整大小适应新容器
        item_bg = RoundedRectangle(
            width=5.5,  # 适应新的容器宽度
            height=0.8,
            corner_radius=0.25,
            fill_color=WHITE,
            fill_opacity=0.06,
            stroke_color=WHITE,
            stroke_width=0.8,
            stroke_opacity=0.15,
        )
        item_bg.move_to(item_positions[i])

        # 图标背景 - 使用项目主题色
        icon_bg = RoundedRectangle(
            width=0.7, height=0.7, corner_radius=0.15, fill_color=item_data["color"], fill_opacity=0.15, stroke_width=0
        )
        icon_bg.move_to(item_positions[i] + LEFT * 2.3)  # 调整图标位置适应新宽度

        # 修复左边图标显示 - 使用默认图标
        default_icons = ["☀️", "😎", "💨", "🧊", "⭐"]
        icon = Text(
            default_icons[i % len(default_icons)],
            font_size=32,  # 调整图标字体大小
            color=item_data["color"],  # 使用项目主题色而不是白色
            weight=BOLD,
        )
        icon.move_to(icon_bg.get_center())

        # 如果图标仍然不显示，添加一个备用的圆形图标
        icon_fallback = Circle(
            radius=0.25, fill_color=item_data["color"], fill_opacity=0.8, stroke_color=WHITE, stroke_width=2
        )
        icon_fallback.move_to(icon_bg.get_center())

        # 创建图标组合（优先显示emoji，如果不行则显示圆形）
        icon_display = VGroup(icon_fallback, icon)

        # 主文本 - 优化样式，增强层次感，居中对齐，添加发光效果
        main_text = Text(
            item_data["text"],
            font_size=22,  # 适当调整主文本字体
            color=WHITE,
            weight=BOLD,
            font="Arial",
        )
        # 添加主文本发光效果
        main_text_glow = main_text.copy()
        main_text_glow.set_color(item_data["color"])
        main_text_glow.set_stroke(color=item_data["color"], width=2, opacity=0.6)
        main_text_combined = VGroup(main_text_glow, main_text)
        main_text_combined.move_to(item_positions[i] + UP * 0.12)  # 居中对齐，去掉左偏移

        # 标签文本 - 优化样式，与主文本区分，改为蓝色，添加发光效果
        tags = Text(
            item_data["tags"],
            font_size=16,  # 调整标签字体
            color="#60a5fa",  # 改为蓝色
            font="Arial",
            slant=ITALIC,  # 添加斜体效果
        )
        tags.set_opacity(0.8)
        # 添加标签发光效果
        tags_glow = tags.copy()
        tags_glow.set_color("#87CEEB")
        tags_glow.set_stroke(color="#87CEEB", width=1.5, opacity=0.4)
        tags_combined = VGroup(tags_glow, tags)
        tags_combined.move_to(item_positions[i] + DOWN * 0.18)  # 居中对齐，去掉左偏移

        # 修复右边勾选图标样式，添加发光效果
        check = Text(
            "✓",  # 使用简单的勾号
            font_size=22,
            color="#4ade80",
            weight=BOLD,
        )
        # 添加勾选图标发光效果
        check_glow = check.copy()
        check_glow.set_color("#00ff7f")
        check_glow.set_stroke(color="#00ff7f", width=2, opacity=0.7)
        check_combined = VGroup(check_glow, check)
        check_combined.move_to(item_positions[i] + RIGHT * 2.3)  # 调整勾选位置适应新宽度

        # 组合项目元素
        item_group = VGroup(item_bg, icon_bg, icon_display, main_text_combined, tags_combined, check_combined)
        checklist_items.append(item_group)

    # 组合所有元素，包含标题横线
    all_elements = VGroup(background, container, title_combined, title_line, *checklist_items)

    # 执行动画序列
    logger.info(f"播放纯文本动态展示动画 '{unique_id}'")

    with scene.voiceover(text=narration) as tracker:
        # 1. 背景渐入效果
        scene.play(FadeIn(background), run_time=1.2)

        # 2. 容器出现
        scene.play(FadeIn(container), run_time=0.8)

        # 3. 标题和下划线一起快速展示
        title_combined.scale(0.1).rotate(PI / 4).shift(DOWN * 3)
        title_line.scale(0.1).shift(DOWN * 3)
        scene.play(
            AnimationGroup(
                title_combined.animate.scale(10).rotate(-PI / 4).shift(UP * 3),
                title_line.animate.scale(10).shift(UP * 3),
                lag_ratio=0.1,
            ),
            rate_func=rate_functions.ease_out_bounce,
            run_time=1.2,
        )

        # 标题发光脉冲效果
        scene.play(title_glow.animate.set_stroke(width=6, opacity=1.0), rate_func=there_and_back, run_time=0.5)

        # 4. 逐个显示清单项目 - 文本框内元素一起进入，但文字有逐字效果
        for i, (item_group, item_data) in enumerate(zip(checklist_items, items)):
            item_bg, icon_bg, icon_display, main_text_combined, tags_combined, check_combined = item_group
            main_text_glow, main_text = main_text_combined
            tags_glow, tags = tags_combined
            check_glow, check = check_combined

            # 整个项目从左滑入
            item_group.shift(LEFT * 8)
            scene.play(item_group.animate.shift(RIGHT * 8), rate_func=rate_functions.ease_out_back, run_time=0.8)

            # 图标发光脉冲
            scene.play(
                AnimationGroup(
                    # 图标发光效果
                    icon_display[1].animate.set_stroke(color=item_data["color"], width=3, opacity=0.8),
                    # 主文本逐字显示效果
                    Write(main_text, run_time=1.0),
                    Write(main_text_glow, run_time=1.0),
                    # 标签逐字显示效果
                    Write(tags, run_time=0.8),
                    Write(tags_glow, run_time=0.8),
                    # 勾选发光效果
                    check_glow.animate.set_stroke(width=5, opacity=1.0),
                    lag_ratio=0.1,
                ),
                run_time=1.2,
            )

            # 所有发光效果回归正常
            scene.play(
                AnimationGroup(
                    main_text_glow.animate.set_stroke(width=2, opacity=0.6),
                    tags_glow.animate.set_stroke(width=1.5, opacity=0.4),
                    check_glow.animate.set_stroke(width=2, opacity=0.7),
                    rate_func=there_and_back,
                    run_time=0.5,
                )
            )

            scene.wait(0.2)

        # 6. 整体轻微浮动效果
        scene.play(all_elements.animate.shift(UP * 0.08).rotate(0.02), rate_func=there_and_back, run_time=2.0)

        # 等待语音完成 - 修复时间计算
        # 实际动画时长计算：
        # 背景渐入(1.2) + 容器出现(0.8) + 标题展示(1.2) + 标题发光(0.5) + 每个项目(0.8+1.2+0.5+0.2=2.7) + 整体浮动(2.0)
        estimated_animation_time = 5.7 + (len(items) * 2.7)
        narration_duration = tracker.duration if hasattr(tracker, "duration") else 3.0
        remaining_time = max(0, narration_duration - estimated_animation_time)
        if remaining_time > 0:
            scene.wait(remaining_time)

    scene.current_mobj = all_elements
    scene.save_scene_state("text_only", unique_id)
    logger.info(f"纯文本动态展示动画完成: {unique_id}")
