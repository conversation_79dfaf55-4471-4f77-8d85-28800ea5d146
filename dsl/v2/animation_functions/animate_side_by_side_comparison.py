"""
effect: |
    创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。

use_cases:
    - 对比两种不同的代码实现或算法
    - 比较"之前"与"之后"的状态
    - 并列展示问题和解决方案
    - 对比两个图像、图表或设计

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  left_content:
    type: str
    desc: 左侧内容（文本、代码字符串或图像路径）
    required: true
  left_type:
    type: str
    desc: 左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'
    required: true
  right_content:
    type: str
    desc: 右侧内容（文本、代码字符串或图像路径）
    required: true
  right_type:
    type: str
    desc: 右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown'
    required: true
  left_title:
    type: str
    desc: 左侧窗格的标题
    default: None
  right_title:
    type: str
    desc: 右侧窗格的标题
    default: None
  transition:
    type: str
    desc: 内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none'
    default: fadeIn
  narration:
    type: str
    desc: 在内容显示时播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_side_by_side_comparison
    params:
      left_content: |
        def fib_recursive(n):
            if n <= 1:
                return n
            return fib_recursive(n-1) + fib_recursive(n-2)
      left_type: code
      left_title: 递归斐波那契
      right_content: |
        def fib_iterative(n):
            a, b = 0, 1
            for _ in range(n):
                a, b = b, a + b
            return a
      right_type: code
      right_title: 迭代斐波那契
      narration: 让我们比较斐波那契数列的递归和迭代实现。
  - type: animate_side_by_side_comparison
    params:
      left_content: |
        {
            "name": "Python",
            "year": 1991,
            "creator": "Guido van Rossum",
            "paradigms": ["面向对象", "命令式", "函数式"]
        }
      left_type: json
      left_title: Python 信息
      right_content: |
        {
            "name": "Java",
            "year": 1995,
            "creator": "James Gosling",
            "paradigms": ["面向对象", "命令式"]
        }
      right_type: json
      right_title: Java 信息
      transition: fadeIn
      narration: Python 和 Java 的信息对比。
  - type: animate_side_by_side_comparison
    params:
      left_content: |
        # Python

        - 创建于1991年

        - 由Guido van Rossum开发

        - 支持面向对象、命令式和函数式编程

      left_type: markdown
      left_title: Python 信息
      right_content: |
        # Java

        - 创建于1995年

        - 由James Gosling开发

        - 主要支持面向对象编程

      right_type: markdown
      right_title: Java 信息
      transition: fadeIn
      narration: Python 和 Java 的信息对比。
      right_type: markdown
      right_title: Java 信息
      transition: fadeIn
      narration: Python 和 Java 的信息对比。

notes:
  - 对于'image'类型，content应为图像文件的本地路径
  - 对于'code'和'json'类型，会自动应用语法高亮
  - 内容会自动缩放以适应各自的窗格大小
  - 调用此函数会清除屏幕上的其他内容
"""

import os
from typing import TYPE_CHECKING, Optional

from loguru import logger
from manim import *

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene  # Assuming FeynmanScene is here

from dsl.v2.animation_functions.animate_markdown import _create_markdown_mobjects, create_glow_text_simple
from dsl.v2.themes.theme_utils import ThemeUtils

# 使用主题化的字体和大小，但保留默认值作为备选
DEFAULT_FONT = ThemeUtils.get_font("primary", "Microsoft YaHei")
TITLE_FONT_SIZE = ThemeUtils.get_font_size("h2", 32)
CONTENT_FONT_SIZE = ThemeUtils.get_font_size("body", 24)

try:
    from utils.md_to_pango import MarkdownToSimplifiedConverter
except ImportError:
    logger.warning("无法导入 MarkdownToSimplifiedConverter，Markdown 渲染将使用简化实现")
    MarkdownToSimplifiedConverter = None


def _create_content_mobject(
    content_str: str,
    content_type: str,
    pane_rect: Rectangle,  # Rectangle defining the available space for this pane (bg + content)
    side_prefix: str = "item",  # for logging
    bg_color: ManimColor = None,  # 使用主题颜色作为默认值
) -> tuple[VGroup, VMobject, float]:
    """
    Helper function to create a Manim Mobject for a piece of content (text, code, image, etc.),
    including a styled background rectangle.
    """
    actual_content_mob: Mobject

    # Create the background rectangle first, based on pane_rect
    # It will occupy most of the pane_rect area

    # 如果没有指定背景颜色，使用主题系统的surface颜色
    bg_stroke_color = ThemeUtils.get_color("surface", DARK_GRAY)

    # 使用主题系统的比较面板专用设置
    # 圆角设置
    corner_radius = ThemeUtils.get_component_style("comparison", "panel_corner_radius", 0.5)

    # 面板背景不透明度 - 直接从主题组件中获取，不同主题已经在theme_manager中设置好不同的值
    fill_opacity = ThemeUtils.get_component_style("comparison", "panel_background_opacity", 0.5)

    bg_rect = RoundedRectangle(
        width=pane_rect.width * 0.95,  # Background uses almost all of the pane_rect width
        height=pane_rect.height * 0.95,  # Background uses almost all of the pane_rect height
        fill_color=bg_color,
        fill_opacity=fill_opacity,
        corner_radius=corner_radius,
        stroke_color=bg_stroke_color,
        stroke_width=2,
    )

    # Content should fit within this background, with some padding
    content_max_width = bg_rect.width * 0.9
    content_max_height = bg_rect.height * 0.9

    if content_type == "text":
        actual_content_mob = create_glow_text_simple(content_str, CONTENT_FONT_SIZE, line_spacing=1.2)
    elif content_type == "code":
        actual_content_mob = Code(
            code_string=content_str, language="python", formatter_style="monokai"
        )  # Slightly smaller for code
    elif content_type == "json":
        actual_content_mob = Code(code_string=content_str, language="json", formatter_style="monokai")
    elif content_type == "image":
        if not os.path.exists(content_str):
            logger.error(f"Image file not found for side-by-side {side_prefix}: {content_str}")
            error_color = ThemeUtils.get_color("error", RED)
            error_font_size = ThemeUtils.get_font_size("small", 20)
            actual_content_mob = create_glow_text_simple(
                f"Error: Image not found\n{os.path.basename(content_str)}",
                error_font_size,
                custom_color=error_color
            )
        else:
            actual_content_mob = ImageMobject(content_str)
    elif content_type == "markdown":
        if MarkdownToSimplifiedConverter:
            converter = MarkdownToSimplifiedConverter()
            pango_markup = converter.convert(content_str)
        else:  # Fallback if converter is not available
            pango_markup = MarkupText(content_str.replace("\n", "<br/>"))  # Basic fallback
        actual_content_mob = _create_markdown_mobjects(pango_markup)
    else:
        logger.warning(f"Unsupported content type '{content_type}' for side-by-side {side_prefix}. Using Text.")
        actual_content_mob = create_glow_text_simple(f"Unsupported: {content_str[:50]}...", CONTENT_FONT_SIZE)

    # Scale content to fit within the designated content area inside the background
    scale_factor = min(
        content_max_width / actual_content_mob.width, content_max_height / actual_content_mob.height, 1.0
    )

    return bg_rect, actual_content_mob, scale_factor


def animate_side_by_side_comparison(
    scene: "FeynmanScene",
    title: str,
    left_content: str,
    left_type: str,
    right_content: str,
    right_type: str,
    left_title: Optional[str] = None,
    right_title: Optional[str] = None,
    transition: str = "fadeIn",  # "fadeIn", "slideUp", "none"
    narration: Optional[str] = None,
    id: Optional[str] = None,  # Unique ID for the comparison
) -> None:
    logger.info(f"Animating side-by-side comparison: Left='{left_content[:20]}...', Right='{right_content[:20]}...'")

    # Create title object
    title_text = Text(title, font=ThemeUtils.get_font("heading", "Microsoft YaHei"),
                     color=ThemeUtils.get_color("text_primary", WHITE),
                     font_size=ThemeUtils.get_font_size("h1"))

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=title_text)

    unique_id = id or f"sbs_comparison_{abs(hash(left_content + right_content)) % 10000}"

    # Define overall layout parameters
    total_screen_width = scene.camera.frame_width * 0.95
    total_screen_height = scene.camera.frame_height * 0.90
    h_buffer_between_panes = scene.camera.frame_width * 0.05  # Use a percentage for buffer
    v_buffer_title_content = MED_SMALL_BUFF

    # Create title mobjects if they exist
    # 使用主题系统的标题颜色
    title_color = ThemeUtils.get_color("primary", BLUE_D)

    left_title_mob: Optional[Mobject] = None
    if left_title:
        left_title_mob = create_glow_text_simple(left_title, TITLE_FONT_SIZE, custom_color=title_color)

    right_title_mob: Optional[Mobject] = None
    if right_title:
        right_title_mob = create_glow_text_simple(right_title, TITLE_FONT_SIZE, custom_color=title_color)

    # Determine max title height for layout calculation
    max_title_height = 0
    if left_title_mob:
        max_title_height = max(max_title_height, left_title_mob.height)
    if right_title_mob:
        # If one side has a title, the other side's content pane should align as if it also had a title of same height
        # So, we use the max height for both calculations if any title exists.
        max_title_height = max(max_title_height, right_title_mob.height)

    title_area_height = 0
    if max_title_height > 0:
        title_area_height = max_title_height + v_buffer_title_content

    # Calculate dimensions for content panes (where the VGroup with background will go)
    content_pane_height = total_screen_height - title_area_height
    content_pane_width = (total_screen_width - h_buffer_between_panes) / 2

    if content_pane_height <= 0 or content_pane_width <= 0:
        logger.error("Not enough space for side-by-side content after accommodating titles/buffers.")
        # Fallback: create small error message mobjects
        error_color = ThemeUtils.get_color("error", RED)
        error_font_size = ThemeUtils.get_font_size("body", 24)
        error_msg = create_glow_text_simple("Layout Error: Not enough space", error_font_size, custom_color=error_color)
        scene.add(error_msg)
        scene.wait(2)
        return

    # Create Rectangle objects to define the area for each content pane (bg + content)
    # These are not added to scene, just used for dimensioning _create_content_mobject
    left_pane_rect = Rectangle(width=content_pane_width, height=content_pane_height)
    right_pane_rect = Rectangle(width=content_pane_width, height=content_pane_height)

    # Create content mobjects (VGroup of background + actual content)
    # 使用主题系统的左右内容背景颜色
    left_bg_color: str = ThemeUtils.get_color("comparison_left_bg", WHITE)
    right_bg_color: str = ThemeUtils.get_color("comparison_right_bg", WHITE)

    left_bg_rect, left_content_mob, left_scale_factor = _create_content_mobject(
        left_content, left_type, left_pane_rect, side_prefix=f"{unique_id}_left", bg_color=left_bg_color
    )
    right_bg_rect, right_content_mob, right_scale_factor = _create_content_mobject(
        right_content, right_type, right_pane_rect, side_prefix=f"{unique_id}_right", bg_color=right_bg_color
    )
    scale_factor = min(left_scale_factor, right_scale_factor)
    logger.info(
        f"Scale factor: {scale_factor}, left scale factor: {left_scale_factor}, right scale factor: {right_scale_factor}"
    )
    left_content_mob.scale(scale_factor)
    right_content_mob.scale(scale_factor)
    left_content_mob.move_to(left_bg_rect.get_center())
    right_content_mob.move_to(right_bg_rect.get_center())

    left_content_pane = Group(left_bg_rect, left_content_mob)
    right_content_pane = Group(right_bg_rect, right_content_mob)

    # Assemble final left and right elements (title + content_pane)
    final_left_element: Group
    if left_title_mob:
        # Align title top, content_pane bottom, relative to the space allocated for them
        left_title_mob.align_to(ORIGIN, UP)  # Placeholder, will be positioned by VGroup
        left_content_pane.align_to(ORIGIN, DOWN)  # Placeholder
        final_left_element = Group(left_title_mob, left_content_pane).arrange(
            DOWN, buff=v_buffer_title_content, aligned_edge=LEFT
        )
    else:
        final_left_element = left_content_pane

    final_right_element: Group
    if right_title_mob:
        right_title_mob.align_to(ORIGIN, UP)
        right_content_pane.align_to(ORIGIN, DOWN)
        final_right_element = Group(right_title_mob, right_content_pane).arrange(
            DOWN, buff=v_buffer_title_content, aligned_edge=LEFT
        )
    else:
        final_right_element = right_content_pane

    # Group the two final elements
    comparison_group = Group(final_left_element, final_right_element).arrange(RIGHT, buff=h_buffer_between_panes)

    # Final scaling and positioning of the entire comparison group
    # This ensures it fits within the overall screen limits and is centered.
    if comparison_group.width > total_screen_width:
        comparison_group.scale_to_fit_width(total_screen_width)
    if comparison_group.height > total_screen_height:
        # This check is crucial if titles make one side taller before VGroup arrangement
        comparison_group.scale_to_fit_height(total_screen_height)

    comparison_group.move_to(ORIGIN)

    # Animation
    animation_duration = 1.0  # Default duration for animations like slideUp

    logger.info(f"Playing side-by-side '{unique_id}' with narration.")
    with scene.voiceover(text=narration) as tracker:
        # Handle animation section for transition support
        if not scene.transition_enabled:
            # Normal animation - show title first
            title_text.to_edge(UP, buff=0.8)
            scene.play(Write(title_text), run_time=1.0)
            scene.wait(0.5)

        # Always animate the comparison content with original effects
        wait_duration = (
            tracker.duration if hasattr(tracker, "duration") and tracker.duration else 3.0
        )  # Default if no duration
        if transition == "fadeIn":
            scene.play(FadeIn(comparison_group, shift=RIGHT), run_time=animation_duration)
        elif transition == "slideUp":
            comparison_group.shift(
                DOWN * (scene.camera.frame_height / 2 + comparison_group.height / 2)
            )  # Start off-screen bottom
            scene.add(comparison_group)  # Add it first, then animate
            scene.play(comparison_group.animate.move_to(ORIGIN), run_time=animation_duration)
        else:  # "none" or default
            scene.add(comparison_group)
        scene.wait(wait_duration)  # Wait for narration to finish

    # Create display group with title and comparison content
    display_group = Group(title_text, comparison_group)
    display_group.arrange(DOWN, buff=0.5)

    # Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state("sbs_comparison", unique_id)
    logger.info(f"Finished animating side-by-side comparison '{unique_id}'.")
