"""
FeynmanScene class for Manim animations with region management.
"""

from pathlib import Path
from typing import Optional

from loguru import logger
from manim import *
from manim_voiceover import VoiceoverScene

from dsl.v2.core.transition_effects import TransitionManager
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.common import Config
from utils.edgetts_service import EdgeTTSService
from utils.feynman_scene import add_wrapped_subcaption
from utils.transition_helper import load_scene_state, save_scene_state

VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption


class FeynmanScene(VoiceoverScene):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = logger  # 使 logger 在场景实例中可用
        self.set_speech_service(EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"), create_subcaption=True)
        self.current_mobj: Mobject | None = None
        self.full_screen_rect = Rectangle(height=config.frame_height, width=config.frame_width)
        transition_config = Config().config.get("transition", {})

        # 转场相关属性
        self.transition_enabled = transition_config.get("enable", False)  # 是否启用转场效果
        self.default_transition_type = transition_config.get("type", None)  # 默认转场类型，None表示随机选择
        self.transition_run_time = transition_config.get("run_time", 1.0)  # 转场动画时长

        # 场景状态序列化相关
        self.scene_states_dir = Path(transition_config.get("state_dir", "output/scene_states"))
        self.scene_states_dir.mkdir(parents=True, exist_ok=True)
        self.current_scene_id = None

    def clear_current_mobj(
        self,
        transition_type: Optional[str] = None,
        run_time: Optional[float] = None,
        new_mobj: Optional[Mobject] = None,
    ):
        """
        清除当前场景中的对象，使用精美的转场效果

        Args:
            transition_type: 转场效果类型，None表示随机选择
            run_time: 转场动画时长，None使用默认值
        """
        if self.current_mobj:
            if not self.transition_enabled:
                # 如果禁用转场，使用原来的简单淡出
                logger.warning("Transition is disabled, using simple fade out")
                self.play(FadeOut(self.current_mobj))
            else:
                # 使用转场效果
                actual_transition_type = transition_type or self.default_transition_type
                logger.info(
                    f"actual_transition_type: {actual_transition_type}, default: {self.default_transition_type}, input: {transition_type}"
                )
                actual_run_time = run_time or self.transition_run_time

                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=self.current_mobj,
                    new_mobj=new_mobj,
                    transition_type=actual_transition_type,
                    run_time=actual_run_time,
                )

            self.remove(self.current_mobj)
            self.current_mobj = None

    def save_scene_state(self, content_type: str, mobject_id: str):
        if not hasattr(self, "first_mobj"):
            scene_name = f"{self.__class__.__name__}_first"
            self.first_mobj = self.current_mobj
        else:
            scene_name = f"{self.__class__.__name__}_last"
        save_scene_state(
            scene_name=scene_name,
            current_mobj=self.current_mobj,
            save_dir=str(self.scene_states_dir),
            content_type=content_type,
            mobject_id=mobject_id,
        )

    def load_scene_state(self, scene_id: str, is_old_mobject: bool = True) -> Optional[Mobject]:
        if is_old_mobject:
            scene_name = f"{scene_id}_last"
            if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                # 可能这个分镜只有一个action，所以没有_last，只有_first
                scene_name = f"{scene_id}_first"
                if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                    # 兼容直接以mobject_id为名字存储的文件
                    scene_name = scene_id
        else:
            scene_name = f"{scene_id}_first"
        return load_scene_state(save_dir=str(self.scene_states_dir), scene_id=scene_name, is_old_mobject=is_old_mobject)

    def create_hyperbolic_network_background(self):
        # 获取主题颜色
        primary_color = ThemeUtils.get_color("primary")
        background_color = ThemeUtils.get_color("background")

        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=config.frame_width, height=config.frame_height, fill_opacity=1, stroke_width=0
        ).set_color_by_gradient([primary_color, background_color, primary_color])

        # Create hyperbolic network with regular pattern
        network = VGroup()

        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10

        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=primary_color,
            )
            network.add(line)

        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(radius=radius, stroke_width=1.2, stroke_opacity=0.3, stroke_color=primary_color)
            network.add(circle)

        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines

                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles

                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])

                # Create a curved path between points
                control = (
                    np.array([(start[0] + end[0]) * 0.5, (start[1] + end[1]) * 0.5, 0]) * 0.5
                )  # Pull control point toward center for hyperbolic effect

                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=primary_color,
                )
                network.add(curve)

        # Scale the network to fit the screen
        network.scale(0.9)

        # Add a central node
        central_node = Circle(
            radius=0.15, fill_opacity=0.5, stroke_width=1.5, stroke_color=primary_color, fill_color=primary_color
        )

        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])

                node = Circle(
                    radius=0.08, fill_opacity=0.4, stroke_width=1, stroke_color=primary_color, fill_color=primary_color
                ).move_to(position)
                nodes.add(node)

        network.add(central_node, nodes)

        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=background_color,
        )

        return gradient, network, center_mask

    def add_background(self):
        # Create background elements
        gradient, network, center_mask = self.create_hyperbolic_network_background()

        # Add the background to the scene
        self.add(gradient, center_mask)

        # Add the network and start rotation animation in the background
        self.add(network)

        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # Rotate at a speed of 0.1 radians per dt

        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.add(network)
