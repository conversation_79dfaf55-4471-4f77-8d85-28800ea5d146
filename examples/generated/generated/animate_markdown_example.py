# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_markdown_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_markdown
        animate_markdown(
            scene=self,
            content="# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
            title="这是一个Markdown示例",
            narration="这是一个Markdown示例，包含标题、文本和代码。"
        )

        # Action 2: animate_markdown
        animate_markdown(
            scene=self,
            content="## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n| C产品 | ¥399 | 4.9分 |\n",
            title="产品价格比较",
            narration="这个表格比较了两款产品的价格和评分。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
